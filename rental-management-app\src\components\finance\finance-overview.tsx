import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowUpRight, ArrowDownRight, TrendingUp, Wallet } from 'lucide-react';

// 模拟财务数据
const mockFinanceData = {
  summary: {
    totalIncome: 87600,
    totalExpense: 23450,
    netIncome: 64150,
    incomeGrowth: 5.2,
    expenseGrowth: 2.8,
  },
  monthly: [
    { month: '1月', income: 14500, expense: 3800, net: 10700 },
    { month: '2月', income: 14500, expense: 4200, net: 10300 },
    { month: '3月', income: 14500, expense: 3600, net: 10900 },
    { month: '4月', income: 14600, expense: 3950, net: 10650 },
    { month: '5月', income: 14700, expense: 4100, net: 10600 },
    { month: '6月', income: 14800, expense: 3800, net: 11000 },
  ],
  categories: {
    income: [
      { name: '租金收入', amount: 81600, percentage: 93.2 },
      { name: '押金收入', amount: 5000, percentage: 5.7 },
      { name: '其他收入', amount: 1000, percentage: 1.1 },
    ],
    expense: [
      { name: '维修费用', amount: 8500, percentage: 36.2 },
      { name: '物业费', amount: 7200, percentage: 30.7 },
      { name: '水电费', amount: 4800, percentage: 20.5 },
      { name: '税费', amount: 2100, percentage: 8.9 },
      { name: '其他支出', amount: 850, percentage: 3.7 },
    ],
  },
};

const FinanceOverview = () => {
  return (
    <div className="space-y-6">
      {/* 财务概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">总收入</CardTitle>
            <ArrowUpRight className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{mockFinanceData.summary.totalIncome.toLocaleString()}</div>
            <p className="text-xs text-gray-500 mt-1">
              较上月 <span className="text-green-600">↑{mockFinanceData.summary.incomeGrowth}%</span>
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">总支出</CardTitle>
            <ArrowDownRight className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{mockFinanceData.summary.totalExpense.toLocaleString()}</div>
            <p className="text-xs text-gray-500 mt-1">
              较上月 <span className="text-red-600">↑{mockFinanceData.summary.expenseGrowth}%</span>
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">净收入</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{mockFinanceData.summary.netIncome.toLocaleString()}</div>
            <p className="text-xs text-gray-500 mt-1">
              占总收入 <span className="text-blue-600">{((mockFinanceData.summary.netIncome / mockFinanceData.summary.totalIncome) * 100).toFixed(1)}%</span>
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 财务趋势图表 */}
      <Card>
        <CardHeader>
          <CardTitle>财务趋势</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="monthly" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="monthly">月度</TabsTrigger>
              <TabsTrigger value="quarterly">季度</TabsTrigger>
              <TabsTrigger value="yearly">年度</TabsTrigger>
            </TabsList>
            
            <TabsContent value="monthly" className="space-y-4">
              {/* 这里应该是图表，但我们用表格代替 */}
              <div className="mt-4 overflow-x-auto">
                <div className="h-64 flex items-end justify-between px-2">
                  {mockFinanceData.monthly.map((item, index) => (
                    <div key={index} className="flex flex-col items-center">
                      <div className="flex space-x-1">
                        <div 
                          className="w-8 bg-green-500 rounded-t"
                          style={{ height: `${item.income / 200}px` }}
                        ></div>
                        <div 
                          className="w-8 bg-red-500 rounded-t"
                          style={{ height: `${item.expense / 200}px` }}
                        ></div>
                        <div 
                          className="w-8 bg-blue-500 rounded-t"
                          style={{ height: `${item.net / 200}px` }}
                        ></div>
                      </div>
                      <div className="mt-2 text-xs">{item.month}</div>
                    </div>
                  ))}
                </div>
                <div className="flex justify-center mt-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-1"></div>
                      <span className="text-xs">收入</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
                      <span className="text-xs">支出</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-blue-500 rounded-full mr-1"></div>
                      <span className="text-xs">净收入</span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="quarterly">
              <div className="h-64 flex items-center justify-center text-gray-500">
                季度数据加载中...
              </div>
            </TabsContent>
            
            <TabsContent value="yearly">
              <div className="h-64 flex items-center justify-center text-gray-500">
                年度数据加载中...
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 收入和支出分类 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 收入分类 */}
        <Card>
          <CardHeader>
            <CardTitle>收入分类</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockFinanceData.categories.income.map((category, index) => (
                <div key={index}>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium">{category.name}</span>
                    <span className="text-sm">¥{category.amount.toLocaleString()}</span>
                  </div>
                  <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-green-500 rounded-full"
                      style={{ width: `${category.percentage}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between items-center mt-1">
                    <span className="text-xs text-gray-500">{category.percentage}%</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        
        {/* 支出分类 */}
        <Card>
          <CardHeader>
            <CardTitle>支出分类</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockFinanceData.categories.expense.map((category, index) => (
                <div key={index}>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium">{category.name}</span>
                    <span className="text-sm">¥{category.amount.toLocaleString()}</span>
                  </div>
                  <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-red-500 rounded-full"
                      style={{ width: `${category.percentage}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between items-center mt-1">
                    <span className="text-xs text-gray-500">{category.percentage}%</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default FinanceOverview;