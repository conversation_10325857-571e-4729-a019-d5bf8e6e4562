# Copyright (c) 2025 Aonis. All rights reserved.
import requests

# 先进行认证
def authenticate():
    auth_url = "http://localhost:8000/auth/token"
    # 这里使用默认的管理员账户，实际使用时需要根据实际情况修改
    auth_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(auth_url, data=auth_data)
    if response.status_code == 200:
        token_data = response.json()
        return token_data["access_token"]
    else:
        print(f"认证失败: {response.status_code} - {response.text}")
        return None

def test_parking_api(token):
    # 测试创建车位
    url = "http://localhost:8000/parking/"
    
    # 准备测试数据
    parking_data = {
        "space_number": "B-001",
        "name": "二号车位",
        "location": "地下一层B区",
        "parking_type": "地下车位",
        "floor": -1,
        "zone": "B区",
        "monthly_rent": 350.0,
        "payment_method": "半年付",
        "min_lease_months": 6,
        "status": "可用",
        "description": "靠近电梯的车位"
    }
    
    # 设置认证头
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    # 发送POST请求创建车位
    response = requests.post(url, json=parking_data, headers=headers)
    
    # 打印响应结果
    print(f"创建车位状态码: {response.status_code}")
    print(f"创建车位响应: {response.text}")
    
    # 如果创建成功，尝试获取车位信息
    if response.status_code == 201:
        created_parking = response.json()
        parking_id = created_parking.get('id')
        print(f"\n创建的车位ID: {parking_id}")
        
        # 获取车位详情
        get_url = f"http://localhost:8000/parking/{parking_id}"
        get_response = requests.get(get_url, headers=headers)
        print(f"\n获取车位详情状态码: {get_response.status_code}")
        print(f"车位详情: {get_response.text}")

if __name__ == "__main__":
    # 先认证
    token = authenticate()
    if token:
        print("认证成功，开始测试车位API...")
        test_parking_api(token)
    else:
        print("认证失败，无法测试车位API")