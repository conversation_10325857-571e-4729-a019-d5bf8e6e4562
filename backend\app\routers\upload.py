# Copyright (c) 2025 云福阁. All rights reserved.
from fastapi import APIRouter, UploadFile, HTTPException
from fastapi.responses import FileResponse
import os
import uuid
from pathlib import Path
import mimetypes
from PIL import Image
import io
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/upload", tags=["文件上传"])

# 配置
BASE_DIR = Path(__file__).parent.parent.parent  # 获取项目根目录
UPLOAD_DIR = BASE_DIR / "uploads"  # 上传目录
ALLOWED_EXTENSIONS = {".jpg", ".jpeg", ".png", ".gif", ".webp"}  # 允许的文件扩展名
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
MAX_IMAGE_WIDTH = 1920  # 最大图片宽度
MAX_IMAGE_HEIGHT = 1080  # 最大图片高度

# 确保上传目录存在
UPLOAD_DIR.mkdir(exist_ok=True)
(UPLOAD_DIR / "properties").mkdir(exist_ok=True)



def validate_image(file: UploadFile) -> None:
    """验证图片文件"""
    # 检查文件扩展名
    file_ext = Path(file.filename or "").suffix.lower()
    if file_ext not in ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的文件格式。支持的格式: {', '.join(ALLOWED_EXTENSIONS)}"
        )
    
    # 检查文件大小
    if file.size and file.size > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"文件大小超过限制。最大允许: {MAX_FILE_SIZE // (1024*1024)}MB"
        )

def compress_image(image_data: bytes, filename: str) -> bytes:
    """压缩图片"""
    try:
        with Image.open(io.BytesIO(image_data)) as img:
            # 转换为RGB模式（处理RGBA等格式）
            if img.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            
            # 调整尺寸
            if img.width > MAX_IMAGE_WIDTH or img.height > MAX_IMAGE_HEIGHT:
                img.thumbnail((MAX_IMAGE_WIDTH, MAX_IMAGE_HEIGHT), Image.Resampling.LANCZOS)
            
            # 保存压缩后的图片
            output = io.BytesIO()
            format_map = {'.jpg': 'JPEG', '.jpeg': 'JPEG', '.png': 'PNG', '.gif': 'GIF', '.webp': 'WEBP'}
            file_ext = Path(filename).suffix.lower()
            img_format = format_map.get(file_ext, 'JPEG')
            
            if img_format == 'JPEG':
                img.save(output, format=img_format, quality=85, optimize=True)
            else:
                img.save(output, format=img_format, optimize=True)
            
            return output.getvalue()
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"图片处理失败: {str(e)}")

@router.post("/property-images/{property_id}")
async def upload_property_images(
    property_id: int,
    files: list[UploadFile],
    image_type: str = "interior"
) -> dict[str, str | list[dict[str, str | int]]]:
    """上传房屋图片（支持多文件和图片类型分类）"""
    logger.info(f"开始上传{image_type}类型图片，房屋ID: {property_id}，文件数量: {len(files)}")
    
    if len(files) > 10:
        logger.warning(f"上传文件数量超过限制: {len(files)}")
        raise HTTPException(status_code=400, detail="最多只能上传10张图片")

    # 验证图片类型
    valid_types = ["interior", "certificate"]
    if image_type not in valid_types:
        logger.warning(f"无效的图片类型: {image_type}")
        raise HTTPException(status_code=400, detail=f"无效的图片类型。支持的类型: {', '.join(valid_types)}")

    uploaded_files: list[dict[str, str | int]] = []

    # 根据房屋ID和图片类型创建子目录
    property_dir = UPLOAD_DIR / "properties" / str(property_id) / image_type
    property_dir.mkdir(parents=True, exist_ok=True)
    logger.info(f"创建目录: {property_dir}")
    
    for file in files:
        try:
            logger.info(f"开始处理文件: {file.filename}, 大小: {file.size} bytes")
            # 验证文件
            validate_image(file)
            logger.info(f"文件验证通过: {file.filename}")
            
            # 生成唯一文件名
            file_ext = Path(file.filename or "").suffix.lower()
            unique_filename = f"{uuid.uuid4()}{file_ext}"
            file_path = property_dir / unique_filename
            
            # 读取文件内容
            content = await file.read()
            logger.info(f"读取文件内容完成: {file.filename}, 大小: {len(content)} bytes")
            
            # 压缩图片
            compressed_content = compress_image(content, file.filename or "")
            logger.info(f"图片压缩完成: {file.filename}, 压缩后大小: {len(compressed_content)} bytes")
            
            # 保存文件
            with open(file_path, "wb") as f:
                _ = f.write(compressed_content)  # 忽略返回的字节数
            logger.info(f"文件保存完成: {file_path}")
            
            # 生成访问URL
            file_url = f"/static/properties/{property_id}/{image_type}/{unique_filename}"
            
            # 根据文件名智能识别房证类型
            description = file.filename or ""
            if image_type == "certificate":
                if "房产证" in description:
                    description = "房产证"
                elif "土地证" in description:
                    description = "土地证"
                elif "不动产证" in description:
                    description = "不动产证"
                else:
                    description = "房证照片"
            
            uploaded_files.append({
                "filename": file.filename or "",
                "url": file_url,
                "size": len(compressed_content),
                "image_type": image_type,
                "description": description
            })
            logger.info(f"文件处理完成: {file.filename}")
            
        except HTTPException:
            logger.error(f"HTTP异常: {file.filename}", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"文件上传失败: {file.filename}, 错误: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")
    
    type_name = {"interior": "房屋照片", "exterior": "外观照片", "layout": "户型图", "certificate": "房证照片"}
    return {
        "message": f"成功上传 {len(uploaded_files)} 张{type_name.get(image_type, '图片')}",
        "files": uploaded_files
    }

@router.get("/properties/{property_id}/{image_type}/{filename}")
async def get_property_image(property_id: int, image_type: str, filename: str):
    """获取房屋图片（按房屋ID和类型获取）"""
    # 验证图片类型
    valid_types = ["interior", "certificate"]
    if image_type not in valid_types:
        raise HTTPException(status_code=400, detail=f"无效的图片类型。支持的类型: {', '.join(valid_types)}")

    file_path = UPLOAD_DIR / "properties" / str(property_id) / image_type / filename

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="图片不存在")

    # 获取MIME类型
    mime_type, _ = mimetypes.guess_type(str(file_path))
    if not mime_type:
        mime_type = "application/octet-stream"

    return FileResponse(
        path=str(file_path),
        media_type=mime_type,
        filename=filename
    )

@router.get("/properties/{image_type}/{filename}")
async def get_property_image_legacy(image_type: str, filename: str):
    """获取房屋图片（兼容旧的目录结构）"""
    # 验证图片类型
    valid_types = ["interior", "certificate"]
    if image_type not in valid_types:
        raise HTTPException(status_code=400, detail=f"无效的图片类型。支持的类型: {', '.join(valid_types)}")

    # 尝试旧的目录结构
    file_path = UPLOAD_DIR / "properties" / image_type / filename

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="图片不存在")
    
    # 获取MIME类型
    mime_type, _ = mimetypes.guess_type(str(file_path))
    if not mime_type:
        mime_type = "application/octet-stream"
    
    return FileResponse(
        path=str(file_path),
        media_type=mime_type,
        filename=filename
    )

@router.delete("/properties/{image_type}/{filename}")
async def delete_property_image(image_type: str, filename: str):
    """删除房屋图片（支持按类型删除）"""
    file_path = UPLOAD_DIR / "properties" / image_type / filename
    
    if not file_path.exists():
        # 兼容旧的文件路径
        old_file_path = UPLOAD_DIR / "properties" / filename
        if old_file_path.exists():
            file_path = old_file_path
        else:
            raise HTTPException(status_code=404, detail="图片不存在")
    
    try:
        os.remove(file_path)
        return {"message": "图片删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@router.delete("/properties/{filename}")
async def delete_property_image_legacy(filename: str):
    """删除房屋图片（兼容旧接口）"""
    file_path = UPLOAD_DIR / "properties" / filename
    
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="图片不存在")
    
    try:
        os.remove(file_path)
        return {"message": "图片删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@router.post("/property-certificates")
async def upload_property_certificates(files: list[UploadFile], property_id: int) -> dict[str, str | list[dict[str, str | int]]]:
    """专门上传房证照片的接口"""
    return await upload_property_images(files, property_id=property_id, image_type="certificate")
