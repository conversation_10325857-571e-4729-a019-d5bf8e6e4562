"""
房屋数据服务
处理房屋相关的复杂数据操作
"""
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_
from typing import List, Optional, Dict, Any
from ..models import (
    Property, PropertyImage, PropertyFeature, PropertyFacility,
    PropertyStatus, PropertyOrientation, DecorationStatus, PaymentMethod
)
from ..schemas import PropertyCreate, PropertyUpdate
import logging

logger = logging.getLogger(__name__)

class PropertyService:
    """房屋数据服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_property_with_details(self, property_id: int) -> Optional[Property]:
        """获取包含所有详细信息的房屋数据"""
        try:
            property_obj = self.db.query(Property).options(
                joinedload(Property.images),
                joinedload(Property.features),
                joinedload(Property.facilities),
                joinedload(Property.owner),
                joinedload(Property.leases)
            ).filter(Property.id == property_id).first()

            return property_obj
        except Exception as e:
            logger.error(f"获取房屋详情失败: {e}")
            return None
    
    def create_property_with_details(self, property_data: PropertyCreate, owner_id: int) -> Optional[Property]:
        """创建房屋及其相关详细信息"""
        try:
            # 创建房屋基本信息
            property_dict = property_data.dict(exclude={'images', 'features', 'facilities'})
            property_obj = Property(**property_dict, owner_id=owner_id)
            
            self.db.add(property_obj)
            self.db.flush()  # 获取房屋ID
            
            # 添加房屋图片
            if property_data.images:
                for image_data in property_data.images:
                    image_obj = PropertyImage(
                        property_id=property_obj.id,
                        **image_data.dict()
                    )
                    self.db.add(image_obj)
            
            # 添加房屋特色
            if property_data.features:
                for feature_data in property_data.features:
                    feature_obj = PropertyFeature(
                        property_id=property_obj.id,
                        **feature_data.dict()
                    )
                    self.db.add(feature_obj)
            
            # 添加房屋设施
            if property_data.facilities:
                for facility_data in property_data.facilities:
                    facility_obj = PropertyFacility(
                        property_id=property_obj.id,
                        **facility_data.dict()
                    )
                    self.db.add(facility_obj)
            
            self.db.commit()
            self.db.refresh(property_obj)
            
            return self.get_property_with_details(property_obj.id)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建房屋失败: {e}")
            return None
    
    def update_property_with_details(self, property_id: int, property_data: PropertyUpdate) -> Optional[Property]:
        """更新房屋及其相关详细信息"""
        try:
            property_obj = self.db.query(Property).filter(Property.id == property_id).first()
            if not property_obj:
                return None
            
            # 更新房屋基本信息
            property_dict = property_data.dict(exclude={'images', 'features', 'facilities'}, exclude_unset=True)
            for key, value in property_dict.items():
                setattr(property_obj, key, value)
            
            # 更新房屋图片（先删除旧的，再添加新的）
            if property_data.images is not None:
                # 删除旧图片
                self.db.query(PropertyImage).filter(PropertyImage.property_id == property_id).delete()
                
                # 添加新图片
                for image_data in property_data.images:
                    image_obj = PropertyImage(
                        property_id=property_id,
                        **image_data.dict()
                    )
                    self.db.add(image_obj)
            
            # 更新房屋特色
            if property_data.features is not None:
                # 删除旧特色
                self.db.query(PropertyFeature).filter(PropertyFeature.property_id == property_id).delete()
                
                # 添加新特色
                for feature_data in property_data.features:
                    feature_obj = PropertyFeature(
                        property_id=property_id,
                        **feature_data.dict()
                    )
                    self.db.add(feature_obj)
            
            # 更新房屋设施
            if property_data.facilities is not None:
                # 删除旧设施
                self.db.query(PropertyFacility).filter(PropertyFacility.property_id == property_id).delete()
                
                # 添加新设施
                for facility_data in property_data.facilities:
                    facility_obj = PropertyFacility(
                        property_id=property_id,
                        **facility_data.dict()
                    )
                    self.db.add(facility_obj)
            
            self.db.commit()
            
            return self.get_property_with_details(property_id)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新房屋失败: {e}")
            return None
    
    def search_properties(self, 
                         city: Optional[str] = None,
                         property_type: Optional[str] = None,
                         min_rent: Optional[float] = None,
                         max_rent: Optional[float] = None,
                         rooms: Optional[int] = None,
                         status: Optional[PropertyStatus] = None,
                         has_elevator: Optional[bool] = None,
                         has_parking: Optional[bool] = None,
                         orientation: Optional[PropertyOrientation] = None,
                         decoration_status: Optional[DecorationStatus] = None,
                         skip: int = 0,
                         limit: int = 100) -> List[Property]:
        """搜索房屋"""
        try:
            query = self.db.query(Property).options(
                joinedload(Property.images),
                joinedload(Property.features),
                joinedload(Property.facilities)
            )
            
            # 添加搜索条件
            if city:
                query = query.filter(Property.city.ilike(f"%{city}%"))
            
            if property_type:
                query = query.filter(Property.property_type.ilike(f"%{property_type}%"))
            
            if min_rent is not None:
                query = query.filter(Property.monthly_rent >= min_rent)
            
            if max_rent is not None:
                query = query.filter(Property.monthly_rent <= max_rent)
            
            if rooms is not None:
                query = query.filter(Property.rooms == rooms)
            
            if status is not None:
                query = query.filter(Property.status == status)
            
            if has_elevator is not None:
                query = query.filter(Property.has_elevator == has_elevator)
            
            if has_parking is not None:
                query = query.filter(Property.has_parking == has_parking)
            
            if orientation is not None:
                query = query.filter(Property.orientation == orientation)
            
            if decoration_status is not None:
                query = query.filter(Property.decoration_status == decoration_status)
            
            return query.offset(skip).limit(limit).all()
            
        except Exception as e:
            logger.error(f"搜索房屋失败: {e}")
            return []
    
    def get_property_statistics(self, owner_id: Optional[int] = None) -> Dict[str, Any]:
        """获取房屋统计信息"""
        try:
            query = self.db.query(Property)
            if owner_id:
                query = query.filter(Property.owner_id == owner_id)
            
            total_properties = query.count()
            available_properties = query.filter(Property.status == PropertyStatus.AVAILABLE).count()
            rented_properties = query.filter(Property.status == PropertyStatus.RENTED).count()
            maintenance_properties = query.filter(Property.status == PropertyStatus.MAINTENANCE).count()
            
            # 计算平均租金
            avg_rent_result = query.with_entities(Property.monthly_rent).all()
            avg_rent = sum(rent[0] for rent in avg_rent_result) / len(avg_rent_result) if avg_rent_result else 0
            
            return {
                "total_properties": total_properties,
                "available_properties": available_properties,
                "rented_properties": rented_properties,
                "maintenance_properties": maintenance_properties,
                "average_rent": round(avg_rent, 2),
                "occupancy_rate": round((rented_properties / total_properties * 100) if total_properties > 0 else 0, 2)
            }
            
        except Exception as e:
            logger.error(f"获取房屋统计失败: {e}")
            return {}
    
    def add_property_image(self, property_id: int, image_url: str, image_type: str = "interior", 
                          description: Optional[str] = None, is_cover: bool = False) -> Optional[PropertyImage]:
        """添加房屋图片"""
        try:
            # 如果设置为封面图，先取消其他封面图
            if is_cover:
                self.db.query(PropertyImage).filter(
                    and_(PropertyImage.property_id == property_id, PropertyImage.is_cover == True)
                ).update({"is_cover": False})
            
            # 获取当前最大排序号
            max_sort = self.db.query(PropertyImage).filter(
                PropertyImage.property_id == property_id
            ).count()
            
            image_obj = PropertyImage(
                property_id=property_id,
                image_url=image_url,
                image_type=image_type,
                description=description,
                sort_order=max_sort + 1,
                is_cover=is_cover
            )
            
            self.db.add(image_obj)
            self.db.commit()
            self.db.refresh(image_obj)
            
            return image_obj
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"添加房屋图片失败: {e}")
            return None
    
    def delete_property_image(self, image_id: int) -> bool:
        """删除房屋图片"""
        try:
            image_obj = self.db.query(PropertyImage).filter(PropertyImage.id == image_id).first()
            if image_obj:
                self.db.delete(image_obj)
                self.db.commit()
                return True
            return False
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除房屋图片失败: {e}")
            return False