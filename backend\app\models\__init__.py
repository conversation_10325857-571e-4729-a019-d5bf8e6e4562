"""
数据库模型定义（SQLAlchemy 2.0 类型化声明）
定义了系统中使用的所有数据库表结构
"""
from __future__ import annotations

from datetime import datetime

from sqlalchemy import Boolean, DateTime, Enum, Float, ForeignKey, Integer, String, Text, func
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.database import Base
import enum


# 枚举类型定义
class PropertyStatus(str, enum.Enum):
    AVAILABLE = "可用"
    RENTED = "已出租"
    MAINTENANCE = "维修中"
    UNAVAILABLE = "不可用"


class TransactionType(str, enum.Enum):
    INCOME = "收入"
    EXPENSE = "支出"


class TransactionCategory(str, enum.Enum):
    RENT = "租金"
    DEPOSIT = "押金"
    UTILITY = "水电费"
    MAINTENANCE = "维修费"
    PROPERTY_MANAGEMENT = "物业费"
    TAX = "税费"
    INSURANCE = "保险费"
    OTHER = "其他"


class MaintenanceStatus(str, enum.Enum):
    PENDING = "待处理"
    SCHEDULED = "已安排"
    IN_PROGRESS = "进行中"
    COMPLETED = "已完成"
    CANCELLED = "已取消"


class MaintenancePriority(str, enum.Enum):
    LOW = "低"
    MEDIUM = "中"
    HIGH = "高"
    URGENT = "紧急"

class NotificationType(str, enum.Enum):
    INFO = "信息"
    WARNING = "警告"
    SUCCESS = "成功"
    ERROR = "错误"


# 用户模型
class User(Base):
    __tablename__: str = "users"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, index=True)
    email: Mapped[str] = mapped_column(String(100), unique=True, index=True)
    full_name: Mapped[str] = mapped_column(String(100))
    phone: Mapped[str] = mapped_column(String(20))
    hashed_password: Mapped[str] = mapped_column(String(255))
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), onupdate=func.now())

    # 关系
    properties: Mapped[list["Property"]] = relationship("Property", back_populates="owner")
    notifications: Mapped[list["Notification"]] = relationship("Notification", back_populates="user")


# 房屋朝向枚举
class PropertyOrientation(str, enum.Enum):
    SOUTH = "南向"
    NORTH = "北向"
    EAST = "东向"
    WEST = "西向"
    SOUTHEAST = "东南向"
    SOUTHWEST = "西南向"
    NORTHEAST = "东北向"
    NORTHWEST = "西北向"
    SOUTH_NORTH = "南北通透"
    EAST_WEST = "东西通透"


# 装修状态枚举
class DecorationStatus(str, enum.Enum):
    ROUGH = "毛坯"
    SIMPLE = "简装"
    FINE = "精装"
    LUXURY = "豪装"


# 付款方式枚举
class PaymentMethod(str, enum.Enum):
    MONTHLY = "押一付一"
    QUARTERLY = "押一付三"
    SEMI_ANNUAL = "押一付六"
    ANNUAL = "押一付十二"
    CUSTOM = "其他"


# 房屋模型
class Property(Base):
    __tablename__: str = "properties"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), index=True)
    address: Mapped[str] = mapped_column(String(200))
    city: Mapped[str] = mapped_column(String(50))
    province: Mapped[str] = mapped_column(String(50))
    postal_code: Mapped[str] = mapped_column(String(20))
    property_type: Mapped[str] = mapped_column(String(50))  # 住宅类型：公寓、别墅、商铺等
    area: Mapped[float] = mapped_column(Float)  # 建筑面积
    rooms: Mapped[int] = mapped_column(Integer)  # 房间数
    living_rooms: Mapped[int] = mapped_column(Integer, default=1)  # 客厅数
    bathrooms: Mapped[int] = mapped_column(Integer)  # 卫生间数
    floor: Mapped[int | None] = mapped_column(Integer, nullable=True)  # 楼层
    total_floors: Mapped[int | None] = mapped_column(Integer, nullable=True)  # 总楼层
    orientation: Mapped[str | None] = mapped_column(String(20), nullable=True)  # 朝向
    decoration_status: Mapped[str | None] = mapped_column(String(20), nullable=True)  # 装修状态
    has_elevator: Mapped[bool] = mapped_column(Boolean, default=False)  # 是否有电梯
    has_parking: Mapped[bool] = mapped_column(Boolean, default=False)  # 是否有停车位
    description: Mapped[str | None] = mapped_column(Text, nullable=True)  # 房屋描述
    monthly_rent: Mapped[float] = mapped_column(Float)  # 月租金
    deposit: Mapped[float] = mapped_column(Float)  # 押金
    payment_method: Mapped[str] = mapped_column(String(20), default="押一付三")  # 付款方式
    min_lease_months: Mapped[int] = mapped_column(Integer, default=12)  # 最短租期（月）
    status: Mapped[str] = mapped_column(String(20), default="可用")  # 房屋状态
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.getdate())
    updated_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.getdate())
    owner_id: Mapped[int] = mapped_column(ForeignKey("users.id"))

    # 关系
    owner: Mapped["User"] = relationship("User", back_populates="properties")
    leases: Mapped[list["Lease"]] = relationship("Lease", back_populates="property")
    transactions: Mapped[list["Transaction"]] = relationship("Transaction", back_populates="property")
    maintenance_requests: Mapped[list["Maintenance"]] = relationship("Maintenance", back_populates="property")
    images: Mapped[list["PropertyImage"]] = relationship("PropertyImage", back_populates="property", cascade="all, delete-orphan")
    features: Mapped[list["PropertyFeature"]] = relationship("PropertyFeature", back_populates="property", cascade="all, delete-orphan")
    facilities: Mapped[list["PropertyFacility"]] = relationship("PropertyFacility", back_populates="property", cascade="all, delete-orphan")


# 房屋图片模型
class PropertyImage(Base):
    __tablename__: str = "property_images"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    property_id: Mapped[int] = mapped_column(ForeignKey("properties.id"))
    image_url: Mapped[str] = mapped_column(String(500))  # 图片URL
    image_type: Mapped[str] = mapped_column(String(50), default="interior")  # 图片类型：interior, exterior, layout等
    description: Mapped[str | None] = mapped_column(String(200), nullable=True)  # 图片描述
    sort_order: Mapped[int] = mapped_column(Integer, default=0)  # 排序
    is_cover: Mapped[bool] = mapped_column(Boolean, default=False)  # 是否为封面图
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.getdate())

    # 关系
    property: Mapped["Property"] = relationship("Property", back_populates="images")


# 房屋特色模型
class PropertyFeature(Base):
    __tablename__: str = "property_features"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    property_id: Mapped[int] = mapped_column(ForeignKey("properties.id"))
    feature_name: Mapped[str] = mapped_column(String(50))  # 特色名称：如"南北通透"、"精装修"等
    feature_value: Mapped[str | None] = mapped_column(String(100), nullable=True)  # 特色值（可选）
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.getdate())

    # 关系
    property: Mapped["Property"] = relationship("Property", back_populates="features")


# 房屋设施模型
class PropertyFacility(Base):
    __tablename__: str = "property_facilities"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    property_id: Mapped[int] = mapped_column(ForeignKey("properties.id"))
    facility_name: Mapped[str] = mapped_column(String(50))  # 设施名称：如"冰箱"、"洗衣机"等
    facility_category: Mapped[str] = mapped_column(String(30))  # 设施分类：家电、家具、网络等
    is_available: Mapped[bool] = mapped_column(Boolean, default=True)  # 是否可用
    description: Mapped[str | None] = mapped_column(String(200), nullable=True)  # 设施描述
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.getdate())

    # 关系
    property: Mapped["Property"] = relationship("Property", back_populates="facilities")


# 租客模型（基本信息）
class Tenant(Base):
    __tablename__: str = "tenants"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str | None] = mapped_column(String(100), nullable=True, index=True)
    phone: Mapped[str | None] = mapped_column(String(20), nullable=True)
    email: Mapped[str | None] = mapped_column(String(100), nullable=True)
    id_card: Mapped[str | None] = mapped_column(String(30), nullable=True)
    emergency_contact: Mapped[str | None] = mapped_column(String(100), nullable=True)
    emergency_phone: Mapped[str | None] = mapped_column(String(20), nullable=True)
    notes: Mapped[str | None] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=True)
    updated_at: Mapped[datetime] = mapped_column(DateTime, nullable=True)

    # 关系
    leases: Mapped[list["Lease"]] = relationship("Lease", back_populates="tenant")
    transactions: Mapped[list["Transaction"]] = relationship("Transaction", back_populates="tenant")
    maintenance_requests: Mapped[list["Maintenance"]] = relationship("Maintenance", back_populates="tenant")


# 租赁合同模型
class Lease(Base):
    __tablename__: str = "leases"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    tenant_id: Mapped[int] = mapped_column(ForeignKey("tenants.id"))
    property_id: Mapped[int | None] = mapped_column(ForeignKey("properties.id"), nullable=True)
    parking_space_id: Mapped[int | None] = mapped_column(ForeignKey("parking_spaces.id"), nullable=True)
    lease_type: Mapped[str] = mapped_column(String(20))  # 'property' 或 'parking'

    # 租赁信息
    lease_start: Mapped[datetime] = mapped_column(DateTime)
    lease_end: Mapped[datetime] = mapped_column(DateTime)
    monthly_rent: Mapped[float] = mapped_column(Float)
    deposit_paid: Mapped[float] = mapped_column(Float)
    payment_method: Mapped[str] = mapped_column(String(20), default="押一付三")

    # 车位租赁特有字段
    car_number: Mapped[str | None] = mapped_column(String(20), nullable=True)
    car_model: Mapped[str | None] = mapped_column(String(100), nullable=True)

    # 合同状态
    status: Mapped[str] = mapped_column(String(20), default="生效中")  # 生效中、已到期、已终止
    notes: Mapped[str | None] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.getdate())
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.getdate(), onupdate=func.getdate())

    # 关系
    tenant: Mapped["Tenant"] = relationship("Tenant", back_populates="leases")
    property: Mapped["Property | None"] = relationship("Property", back_populates="leases")
    parking_space: Mapped["ParkingSpace | None"] = relationship("ParkingSpace", back_populates="leases")


# 交易记录模型
class Transaction(Base):
    __tablename__: str = "transactions"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    amount: Mapped[float] = mapped_column(Float)
    transaction_type: Mapped[TransactionType] = mapped_column(Enum(TransactionType))
    category: Mapped[TransactionCategory] = mapped_column(Enum(TransactionCategory))
    description: Mapped[str] = mapped_column(Text)
    transaction_date: Mapped[datetime] = mapped_column(DateTime)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now())
    property_id: Mapped[int] = mapped_column(ForeignKey("properties.id"))
    tenant_id: Mapped[int | None] = mapped_column(ForeignKey("tenants.id"), nullable=True)

    # 关系
    property: Mapped["Property"] = relationship("Property", back_populates="transactions")
    tenant: Mapped[Tenant | None] = relationship("Tenant", back_populates="transactions")


# 维修请求模型
class Maintenance(Base):
    __tablename__: str = "maintenance"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    title: Mapped[str] = mapped_column(String(100), index=True)
    description: Mapped[str] = mapped_column(Text)
    status: Mapped[MaintenanceStatus] = mapped_column(Enum(MaintenanceStatus), default=MaintenanceStatus.PENDING)
    priority: Mapped[MaintenancePriority] = mapped_column(Enum(MaintenancePriority), default=MaintenancePriority.MEDIUM)
    reported_date: Mapped[datetime] = mapped_column(DateTime)
    scheduled_date: Mapped[datetime | None] = mapped_column(DateTime, nullable=True)
    completed_date: Mapped[datetime | None] = mapped_column(DateTime, nullable=True)
    cost: Mapped[float | None] = mapped_column(Float, nullable=True)
    notes: Mapped[str | None] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now())
    property_id: Mapped[int] = mapped_column(ForeignKey("properties.id"))
    tenant_id: Mapped[int | None] = mapped_column(ForeignKey("tenants.id"), nullable=True)

    # 关系
    property: Mapped["Property"] = relationship("Property", back_populates="maintenance_requests")
    tenant: Mapped[Tenant | None] = relationship("Tenant", back_populates="maintenance_requests")


# 车位状态枚举
class ParkingStatus(str, enum.Enum):
    AVAILABLE = "可用"
    RENTED = "已出租"
    MAINTENANCE = "维修中"
    UNAVAILABLE = "不可用"


# 车位类型枚举
class ParkingType(str, enum.Enum):
    GROUND = "地面车位"
    UNDERGROUND = "地下车位"
    MECHANICAL = "机械车位"
    COVERED = "有顶车位"


# 通知模型
class Notification(Base):
    __tablename__: str = "notifications"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    title: Mapped[str] = mapped_column(String(100))
    message: Mapped[str] = mapped_column(Text)
    notification_type: Mapped[NotificationType] = mapped_column(Enum(NotificationType), default=NotificationType.INFO)
    read: Mapped[bool] = mapped_column(Boolean, default=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now())
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"))

    # 关系
    user: Mapped["User"] = relationship("User", back_populates="notifications")


# 车位模型
class ParkingSpace(Base):
    __tablename__: str = "parking_spaces"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), index=True)  # 车位名称
    space_number: Mapped[str] = mapped_column(String(50), index=True)  # 车位编号
    location: Mapped[str] = mapped_column(String(200))  # 车位位置
    parking_type: Mapped[str] = mapped_column(String(50), default="地面车位")  # 车位类型
    floor: Mapped[int | None] = mapped_column(Integer, nullable=True)  # 楼层（地下车位）
    zone: Mapped[str | None] = mapped_column(String(50), nullable=True)  # 区域/分区
    monthly_rent: Mapped[float] = mapped_column(Float)  # 月租金
    payment_method: Mapped[str] = mapped_column(String(20), default="半年付")  # 付款方式
    min_lease_months: Mapped[int] = mapped_column(Integer, default=6)  # 最短租期（月）
    status: Mapped[str] = mapped_column(String(20), default="可用")  # 车位状态
    description: Mapped[str | None] = mapped_column(Text, nullable=True)  # 车位描述
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.getdate())
    updated_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.getdate())
    owner_id: Mapped[int] = mapped_column(ForeignKey("users.id"))

    # 关系
    owner: Mapped["User"] = relationship("User")
    leases: Mapped[list["Lease"]] = relationship("Lease", back_populates="parking_space")



