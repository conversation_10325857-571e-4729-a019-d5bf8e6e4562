---
description: Repository Information Overview
alwaysApply: true
---

# Repository Information Overview

## Repository Summary
A rental property management system built with FastAPI (backend) and React/TypeScript (frontend). The system supports property information management, image uploads, tenant management, and other rental-related functionalities.

## Repository Structure
- **backend/**: FastAPI backend service with SQLAlchemy ORM
- **rental-management-app/**: React/TypeScript frontend application
- **start-*.bat**: Batch scripts for starting services

### Main Repository Components
- **Backend API**: FastAPI service for data management and business logic
- **Frontend UI**: React application with TypeScript and Tailwind CSS
- **Startup Scripts**: Batch files for easy deployment and startup

## Projects

### Backend (FastAPI)
**Configuration File**: backend/requirements.txt

#### Language & Runtime
**Language**: Python
**Version**: 3.8+
**Framework**: FastAPI 0.104.0+
**Database ORM**: SQLAlchemy 2.0.43+

#### Dependencies
**Main Dependencies**:
- fastapi: REST API framework
- sqlalchemy: Database ORM
- pyodbc: SQL Server connector
- python-jose: JWT authentication
- passlib: Password hashing
- Pillow: Image processing

#### Build & Installation
```bash
cd backend
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
```

#### Main Files
**Entry Point**: backend/main.py
**API Definition**: backend/app/main.py
**Database Models**: backend/app/models.py
**API Routers**: backend/app/routers/

#### Testing
**Run Command**:
```bash
pytest
```

### Frontend (React)
**Configuration File**: rental-management-app/package.json

#### Language & Runtime
**Language**: TypeScript
**Version**: 5.0+
**Framework**: React 18.3.1
**Build Tool**: Vite 5.1.4

#### Dependencies
**Main Dependencies**:
- react: UI library
- react-router-dom: Routing
- axios: HTTP client
- tailwindcss: CSS framework
- shadcn/ui: UI components (via Radix UI)
- framer-motion: Animations

#### Build & Installation
```bash
cd rental-management-app
npm install
npm run dev
```

#### Main Files
**Entry Point**: rental-management-app/src/main.tsx
**App Component**: rental-management-app/src/App.tsx
**Pages**: rental-management-app/src/pages/
**Components**: rental-management-app/src/components/

#### Build & Deployment
**Development**:
```bash
npm run dev
```
**Production**:
```bash
npm run build
```

## Usage & Operations
**Start Backend**:
```bash
start-backend.bat
```
**Start Frontend**:
```bash
start-frontend.bat
```
**Start Both**:
```bash
start.bat
```

## Database Configuration
**Type**: SQL Server 2008 R2+
**Connection**: Configured in backend/config/database.ini
**Tables**: Properties, users, tenants, transactions, etc.
**Initialization**: backend/app/init_db.py or backend/create_tables.py