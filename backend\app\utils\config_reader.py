"""
配置读取模块
用于从配置文件中读取配置信息
"""
import os
import configparser
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ConfigReader')

def get_config_file_path() -> str:
    """
    获取配置文件路径
    
    返回:
        str: 配置文件路径
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    app_dir = os.path.dirname(current_dir)
    backend_dir = os.path.dirname(app_dir)
    config_file = os.path.join(backend_dir, 'config', 'database.ini')
    return config_file

def read_config() -> configparser.ConfigParser | None:
    """
    读取配置文件
    
    返回:
        Optional[configparser.ConfigParser]: 配置对象，如果读取失败则返回None
    """
    config_file = get_config_file_path()
    
    # 检查配置文件是否存在
    if not os.path.exists(config_file):
        logger.error(f"配置文件不存在: {config_file}")
        return None
    
    # 读取配置文件
    try:
        config = configparser.ConfigParser()
        _ = config.read(config_file)  # 将结果赋值给 _ 变量，避免警告
        return config
    except Exception as e:
        logger.error(f"读取配置文件时出错: {str(e)}")
        return None

def get_db_config() -> dict[str, str] | None:
    """
    从配置文件中读取数据库连接信息
    
    返回:
        Dict[str, str]: 包含数据库连接信息的字典，如果读取失败则返回None
    """
    try:
        config = read_config()
        if not config:
            return None
        
        # 获取MSSQL配置
        if 'mssql' not in config:
            logger.error("配置文件中缺少[mssql]部分")
            return None
        
        mssql_config = config['mssql']
        
        # 检查必要的配置项
        required_keys = ['server', 'database', 'username', 'password', 'driver']
        for key in required_keys:
            if key not in mssql_config:
                logger.error(f"配置文件中缺少必要的配置项: {key}")
                return None
        
        # 返回配置信息
        return {
            'server': mssql_config['server'],
            'database': mssql_config['database'],
            'username': mssql_config['username'],
            'password': mssql_config['password'],
            'driver': mssql_config['driver']
        }
    
    except Exception as e:
        logger.error(f"处理数据库配置时出错: {str(e)}")
        return None

def get_jwt_config() -> dict[str, str | int]:
    """
    从配置文件中读取JWT配置信息
    
    返回:
        Dict[str, Union[str, int]]: 包含JWT配置信息的字典，如果读取失败则生成安全的随机密钥
    """
    import secrets
    
    try:
        config = read_config()
        if not config or 'jwt' not in config:
            # 生成安全的随机密钥
            random_secret_key = secrets.token_hex(32)
            logger.warning("未找到JWT配置，生成随机密钥")
            return {
                'secret_key': random_secret_key,
                'algorithm': "HS256",
                'access_token_expire_minutes': 30
            }
        
        jwt_config = config['jwt']
        
        # 如果配置中的密钥是默认值或为空，生成随机密钥
        secret_key = jwt_config.get('secret_key', "")
        if not secret_key or secret_key == "你的密钥需要更改这个为安全的随机字符串":
            secret_key = secrets.token_hex(32)
            logger.warning("JWT密钥为默认值或为空，已生成随机密钥")
        
        # 返回配置信息，如果某项不存在则使用默认值
        return {
            'secret_key': secret_key,
            'algorithm': jwt_config.get('algorithm', "HS256"),
            'access_token_expire_minutes': int(jwt_config.get('access_token_expire_minutes', 30))
        }
    
    except Exception as e:
        logger.error(f"处理JWT配置时出错: {str(e)}")
        # 生成安全的随机密钥并返回默认配置
        random_secret_key = secrets.token_hex(32)
        return {
            'secret_key': random_secret_key,
            'algorithm': "HS256",
            'access_token_expire_minutes': 30
        }
