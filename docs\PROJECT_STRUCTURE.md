# 项目目录结构

## 根目录
```
retal/
├── .codebuddy/                 # CodeBuddy分析工具目录
├── backend/                    # 后端应用目录
├── docs/                       # 项目文档目录
├── rental-management-app/      # 前端应用目录
├── .gitignore                  # Git忽略文件配置
├── LICENSE                     # 项目许可证文件
└── README.md                   # 项目根说明文档
```

## 后端目录结构 (backend/)
```
backend/
├── app/                        # 应用主目录
│   ├── models.py               # 数据模型定义
│   ├── schemas.py              # 数据验证模式
│   ├── database.py             # 数据库连接配置
│   ├── main.py                 # 应用入口文件
│   ├── config.py               # 应用配置
│   └── routers/                # API路由
│       ├── auth.py             # 认证相关API
│       ├── properties.py        # 物业管理API
│       ├── tenants.py          # 租户管理API
│       ├── leases.py           # 租约管理API
│       ├── transactions.py      # 交易记录API
│       ├── maintenance.py       # 维护管理API
│       ├── notifications.py     # 通知管理API
│       ├── upload.py           # 文件上传API
│       └── parking.py          # 停车管理API
├── tests/                      # 测试文件目录
│   ├── unit/                   # 单元测试
│   ├── integration/            # 集成测试
│   └── api/                    # API测试
│       ├── test_parking.py     # 停车管理API测试
│       ├── test_parking_b.py   # 停车管理API测试
│       └── test_upload.py      # 文件上传API测试
├── scripts/                    # 数据库脚本目录
│   ├── migrations/             # 数据库迁移脚本
│   │   └── create_tables.py    # 创建表结构脚本
│   ├── seeders/                # 数据填充脚本
│   └── utilities/              # 工具脚本
│       ├── check_db.py         # 数据库检查脚本
│       ├── compare_schema.py   # 模式比较脚本
│       └── create_test_image.py # 创建测试图片脚本
├── requirements.txt            # 项目依赖列表
├── .env                        # 环境变量配置文件
└── README.md                   # 后端项目说明文档
```

## 前端目录结构 (rental-management-app/)
```
rental-management-app/
├── public/                     # 静态资源文件夹
├── src/                        # 源代码主目录
│   ├── app/                    # Next.js App Router 目录
│   │   └── layout.tsx          # 应用根布局文件
│   ├── __tests__/              # 测试文件目录
│   │   ├── components/         # 组件测试
│   │   ├── pages/              # 页面测试
│   │   └── services/           # 服务测试
│   ├── components/             # 可复用的UI组件
│   │   ├── auth/               # 认证相关组件
│   │   ├── dashboard/          # 仪表板相关组件
│   │   ├── parking/            # 停车管理相关组件
│   │   ├── properties/         # 物业管理相关组件
│   │   ├── tenants/            # 租户管理相关组件
│   │   ├── leases/             # 租约管理相关组件
│   │   ├── finance/            # 财务管理相关组件
│   │   ├── maintenance/        # 维护管理相关组件
│   │   ├── ui/                 # 通用UI组件
│   │   └── notifications/      # 通知相关组件
│   ├── pages/                  # 页面组件
│   │   ├── auth/               # 认证页面
│   │   ├── dashboard/          # 仪表板页面
│   │   ├── parking/            # 停车管理页面
│   │   ├── properties/         # 物业管理页面
│   │   ├── tenants/            # 租户管理页面
│   │   ├── leases/             # 租约管理页面
│   │   ├── finance/            # 财务管理页面
│   │   ├── maintenance/        # 维护管理页面
│   │   └── notifications/      # 通知页面
│   ├── services/               # API服务和数据获取逻辑
│   ├── hooks/                  # 自定义React Hooks
│   ├── config/                 # 配置文件
│   ├── lib/                    # 工具库和第三方库
│   ├── utils/                  # 通用工具函数
│   ├── styles/                 # 模块化样式体系
│   │   ├── base/              # 基础样式和变量
│   │   │   ├── variables.css # CSS自定义变量系统
│   │   │   └── base.css      # 基础元素样式
│   │   ├── components/        # 组件样式（预留）
│   │   ├── utilities/        # 工具类样式
│   │   │   └── utilities.css # 自定义工具类
│   │   └── globals.css       # 全局样式入口文件
│   ├── App.tsx                 # 应用根组件
│   └── main.tsx                # 应用入口文件
├── package.json                # 项目依赖和脚本配置
├── vite-env.d.ts              # Vite 环境变量类型声明文件
└── README.md                   # 项目说明文档
```

## 文档目录结构 (docs/)
```
docs/
├── PROJECT_STRUCTURE.md        # 项目结构说明文档
├── parking_management/         # 停车管理模块文档
│   ├── ALIGNMENT_parking_management.md   # 需求对齐文档
│   ├── CONSENSUS_parking_management.md   # 需求共识文档
│   ├── DESIGN_parking_management.md      # 系统设计文档
│   ├── TASK_parking_management.md        # 原子任务拆分文档
│   ├── ACCEPTANCE_parking_management.md  # 验收测试文档
│   ├── FINAL_parking_management.md       # 最终总结报告
│   └── TODO_parking_management.md        # 待办事项列表
└── ...
```