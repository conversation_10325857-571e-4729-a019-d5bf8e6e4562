# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 翻译
*.mo
*.pot

# Django相关
*.log
local_settings.py
db.sqlite3

# Flask相关
instance/
.webassets-cache

# Scrapy相关
.scrapy

# Sphinx文档
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat调度文件
celerybeat-schedule

# SageMath解析文件
*.sage.py

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder项目设置
.spyderproject
.spyproject

# Rope项目设置
.ropeproject

# mkdocs文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre类型检查器
.pyre/

# 数据库文件
*.db
*.sqlite
*.sqlite3
rental_management.db

# FastAPI相关
*.pid

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp

# 备份文件
*.bak
*.backup

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo

# 操作系统生成的文件
.DS_Store
Thumbs.db

# 上传文件
media/
static/media/

# 配置文件（可能包含敏感信息）
config.json
secrets.json
.secrets

# 证书文件
*.pem
*.key
*.crt
*.cert