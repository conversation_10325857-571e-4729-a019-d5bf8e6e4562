# 依赖
node_modules
.pnp
.pnp.js

# 测试
coverage

# 生产构建
build
dist
dist-ssr
*.local

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 编辑器
.vscode/
.idea/
*.swp
*.swo

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# TypeScript
*.tsbuildinfo

# Vite
.vite

# 缓存
.cache
.parcel-cache

# 临时文件
*.tmp
*.temp

# 测试覆盖率
.nyc_output
*.lcov

# ESLint缓存
.eslintcache

# Prettier缓存
.prettiercache

# Storybook
.out
.storybook-out
storybook-static

# 备份文件
*.bak
*.backup

# 热重载
.hot-update.*

# 调试
.vscode/launch.json