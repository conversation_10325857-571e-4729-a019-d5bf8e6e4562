# Copyright (c) 2025 云福阁. All rights reserved.
"""
数据库表结构更新脚本
用于更新数据库表结构，保留现有数据
"""
from sqlalchemy import text
from app.database import engine, Base
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def update_tables():
    """更新数据库表结构"""
    try:
        logger.info("开始更新数据库表结构...")
        
        # 更新properties表的枚举字段为字符串字段
        with engine.connect() as conn:
            # 检查表是否存在
            result = conn.execute(text("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'properties'"))
            table_exists = result.scalar() > 0
            
            if table_exists:
                logger.info("更新properties表的字段类型...")
                
                # 更新枚举字段为字符串字段
                try:
                    conn.execute(text("ALTER TABLE properties ALTER COLUMN orientation NVARCHAR(20)"))
                    logger.info("已更新orientation字段")
                except Exception as e:
                    logger.warning(f"更新orientation字段失败: {e}")
                
                try:
                    conn.execute(text("ALTER TABLE properties ALTER COLUMN decoration_status NVARCHAR(20)"))
                    logger.info("已更新decoration_status字段")
                except Exception as e:
                    logger.warning(f"更新decoration_status字段失败: {e}")
                
                try:
                    conn.execute(text("ALTER TABLE properties ALTER COLUMN payment_method NVARCHAR(20)"))
                    logger.info("已更新payment_method字段")
                except Exception as e:
                    logger.warning(f"更新payment_method字段失败: {e}")
                
                try:
                    conn.execute(text("ALTER TABLE properties ALTER COLUMN status NVARCHAR(20)"))
                    logger.info("已更新status字段")
                except Exception as e:
                    logger.warning(f"更新status字段失败: {e}")
                
                conn.commit()
                logger.info("表结构更新完成，数据已保留")
            else:
                # 如果表不存在，创建新表
                Base.metadata.create_all(bind=engine)
                logger.info("数据库表创建成功！")
        
        return True
        
    except Exception as e:
        logger.error(f"更新数据库表失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = update_tables()
    if success:
        print("✅ 数据库表结构更新成功")
    else:
        print("❌ 数据库表结构更新失败")