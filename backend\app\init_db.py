"""
数据库连接检查脚本
用于验证SQL Server数据库连接
"""
from sqlalchemy import text
from .database import engine
from . import models as _models
_ = _models  # 引用以确保模型模块被加载，避免未使用导入告警
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('DB_Init')

def check_connection():
    """检查数据库连接"""
    try:
        with engine.connect() as connection:
            result = connection.execute(text("SELECT @@VERSION"))
            version = result.scalar()
            logger.info(f"数据库连接成功! SQL Server版本: {version}")
            return True
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 检查连接
    _ = check_connection()
