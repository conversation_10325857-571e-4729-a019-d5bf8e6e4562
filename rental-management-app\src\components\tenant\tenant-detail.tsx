import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Phone, 
  Mail, 
  Calendar, 
  Home, 
  Wallet, 
  FileText,  
  Edit, 
  Trash2,
  MessageSquare,
  User
} from 'lucide-react';
import ApiService from '@/services/api';

const TenantDetail = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();
  
  const [tenantDetail, setTenantDetail] = useState<any>(null);
  const [tenantLeases, setTenantLeases] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取租客详情数据
  useEffect(() => {
    const fetchTenantDetail = async () => {
      if (!id) {
        setError('租客ID不存在');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        console.log('获取租客详情，ID:', id);
        
        // 获取租客基本信息
        const tenantData = await ApiService.getTenant(parseInt(id));
        console.log('租客详情数据:', tenantData);
        setTenantDetail(tenantData);
        
        // 获取租客的租赁合同
        const leasesData = await ApiService.getLeases({ tenant_id: parseInt(id) });
        console.log('租客租赁合同:', leasesData);
        setTenantLeases(leasesData || []);
        
        setError(null);
      } catch (error: any) {
        console.error('获取租客详情失败:', error);
        setError(error.response?.data?.detail || '获取租客详情失败');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTenantDetail();
  }, [id]);

  // 删除租客
  const handleDeleteTenant = async () => {
    if (!tenantDetail || !window.confirm('确定要删除这个租客吗？此操作不可撤销。')) {
      return;
    }

    try {
      await ApiService.deleteTenant(tenantDetail.id);
      toast({
        title: '删除成功',
        description: '租客已成功删除',
      });
      navigate('/tenant');
    } catch (error: any) {
      console.error('删除租客失败:', error);
      toast({
        title: '删除失败',
        description: error.message || '删除租客失败，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 获取状态对应的颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case '生效中':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case '即将到期':
        return 'bg-amber-100 text-amber-800 hover:bg-amber-200';
      case '已到期':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  // 加载状态
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 mb-4">{error}</p>
        <Button onClick={() => navigate('/tenant')}>返回租客列表</Button>
      </div>
    );
  }

  // 没有数据
  if (!tenantDetail) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 mb-4">租客信息不存在</p>
        <Button onClick={() => navigate('/tenant')}>返回租客列表</Button>
      </div>
    );
  }

  // 获取主要租赁合同（最新的生效中合同）
  const primaryLease = tenantLeases.find(lease => lease.status === '生效中') || tenantLeases[0];

  return (
    <div className="space-y-6">

      {/* 租客基本信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-2">
          <CardHeader className="flex flex-row items-center space-x-4 pb-2">
            <div className="h-16 w-16 rounded-full overflow-hidden bg-gray-100 flex items-center justify-center">
              <User className="h-8 w-8 text-gray-400" />
            </div>
            <div>
              <CardTitle className="text-xl flex items-center">
                {tenantDetail.name}
                {primaryLease && (
                  <Badge className={`ml-2 ${getStatusColor(primaryLease.status)}`}>
                    {primaryLease.status}
                  </Badge>
                )}
              </CardTitle>
              <div className="flex items-center text-gray-500 mt-1">
                <Phone className="h-4 w-4 mr-1" />
                <span className="text-sm">{tenantDetail.phone}</span>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center text-gray-500">
                  <Mail className="h-4 w-4 mr-2" />
                  <span>电子邮箱: {tenantDetail.email || '未填写'}</span>
                </div>
                <div className="flex items-center text-gray-500">
                  <User className="h-4 w-4 mr-2" />
                  <span>身份证号: {tenantDetail.id_card || '未填写'}</span>
                </div>
                <div className="flex items-center text-gray-500">
                  <Phone className="h-4 w-4 mr-2" />
                  <span>紧急联系人: {tenantDetail.emergency_contact || '未填写'}</span>
                </div>
                <div className="flex items-center text-gray-500">
                  <Phone className="h-4 w-4 mr-2" />
                  <span>紧急联系电话: {tenantDetail.emergency_phone || '未填写'}</span>
                </div>
              </div>
              <div className="space-y-2">
                {primaryLease && (
                  <>
                    <div className="flex items-center text-gray-500">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>租期: {new Date(primaryLease.lease_start).toLocaleDateString()} 至 {new Date(primaryLease.lease_end).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center text-gray-500">
                      <Home className="h-4 w-4 mr-2" />
                      <span>
                        {primaryLease.lease_type === 'property' ? '房屋' : '车位'}: 
                        {primaryLease.property?.name || primaryLease.parking_space?.name || '未知'}
                      </span>
                    </div>
                    {primaryLease.lease_type === 'parking' && primaryLease.car_number && (
                      <div className="flex items-center text-gray-500">
                        <Home className="h-4 w-4 mr-2" />
                        <span>车牌号: {primaryLease.car_number}</span>
                      </div>
                    )}
                  </>
                )}
                {tenantDetail.notes && (
                  <div className="text-gray-500 text-sm">
                    <span>备注: {tenantDetail.notes}</span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex space-x-2 mt-4">
              <Button variant="outline" className="flex-1">
                <MessageSquare className="h-4 w-4 mr-2" />
                发送消息
              </Button>
              <Button variant="outline" className="flex-1">
                <FileText className="h-4 w-4 mr-2" />
                查看合同
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>租金信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {primaryLease ? (
              <>
                <div className="text-2xl font-bold text-blue-600">¥{primaryLease.monthly_rent}/月</div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-500">押金</span>
                    <span>¥{primaryLease.deposit_paid}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">付款方式</span>
                    <span>{primaryLease.payment_method}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">合同状态</span>
                    <Badge className={getStatusColor(primaryLease.status)}>
                      {primaryLease.status}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">租约到期</span>
                    <span>{new Date(primaryLease.lease_end).toLocaleDateString()}</span>
                  </div>
                </div>
                
                <Button className="w-full bg-blue-600 hover:bg-blue-700 mt-2">
                  <Wallet className="h-4 w-4 mr-2" />
                  记录租金支付
                </Button>
              </>
            ) : (
              <div className="text-center text-gray-500 py-4">
                暂无租赁合同信息
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 租赁合同信息 */}
      <Tabs defaultValue="leases" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="leases">租赁合同</TabsTrigger>
          <TabsTrigger value="notes">备注信息</TabsTrigger>
        </TabsList>
        
        {/* 租赁合同内容 */}
        <TabsContent value="leases" className="space-y-4 mt-4">
          {tenantLeases.length > 0 ? (
            <div className="space-y-4">
              {tenantLeases.map((lease) => (
                <Card key={lease.id} className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate(`/contracts/${lease.id}`)}>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="font-semibold text-lg">
                          {lease.lease_type === 'property' ? '房屋租赁' : '车位租赁'}
                        </h3>
                        <p className="text-gray-600">
                          {lease.property?.name || lease.parking_space?.name || '未知'}
                        </p>
                      </div>
                      <Badge className={getStatusColor(lease.status)}>
                        {lease.status}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">租期：</span>
                        <span>{new Date(lease.lease_start).toLocaleDateString()} 至 {new Date(lease.lease_end).toLocaleDateString()}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">月租金：</span>
                        <span className="font-semibold text-blue-600">¥{lease.monthly_rent}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">押金：</span>
                        <span>¥{lease.deposit_paid}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">付款方式：</span>
                        <span>{lease.payment_method}</span>
                      </div>
                      {lease.lease_type === 'parking' && lease.car_number && (
                        <div>
                          <span className="text-gray-500">车牌号：</span>
                          <span>{lease.car_number}</span>
                        </div>
                      )}
                      {lease.lease_type === 'parking' && lease.car_model && (
                        <div>
                          <span className="text-gray-500">车型：</span>
                          <span>{lease.car_model}</span>
                        </div>
                      )}
                    </div>
                    
                    {lease.notes && (
                      <div className="mt-4 p-3 bg-gray-50 rounded">
                        <span className="text-gray-500 text-sm">备注：</span>
                        <p className="text-gray-700 text-sm mt-1">{lease.notes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-gray-500">暂无租赁合同信息</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        {/* 备注信息内容 */}
        <TabsContent value="notes" className="space-y-4 mt-4">
          <Card>
            <CardContent className="p-4">
              {tenantDetail.notes ? (
                <div className="space-y-4">
                  <div className="border-b pb-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">系统备注</span>
                      <span className="text-sm text-gray-500">{new Date(tenantDetail.created_at).toLocaleDateString()}</span>
                    </div>
                    <p className="text-gray-700">{tenantDetail.notes}</p>
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-500 py-4">
                  暂无备注信息
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* 固定底部操作按钮 */}
      <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t shadow-lg" style={{zIndex: 9999}}>
        <div className="flex space-x-3 max-w-md mx-auto">
          <Button
            variant="destructive"
            onClick={handleDeleteTenant}
            className="flex-1 h-12 text-base font-medium bg-red-500 hover:bg-red-600"
          >
            <Trash2 className="h-5 w-5 mr-2" />
            删除租客
          </Button>
          <Button
            variant="outline"
            onClick={() => navigate(`/tenant/${tenantDetail.id}/edit`)}
            className="flex-1 h-12 text-base font-medium border-2 border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-300"
          >
            <Edit className="h-5 w-5 mr-2" />
            编辑租客
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TenantDetail;
