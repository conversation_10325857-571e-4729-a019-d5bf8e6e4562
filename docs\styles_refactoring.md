# 样式体系重构说明

## 重构概述
本次重构将原有的单一 `src/globals.css` 文件迁移到模块化的样式体系结构。

## 变更内容

### 目录结构调整
- 创建 `src/styles/` 目录作为样式体系根目录
- 建立三个子目录：
  - `base/` - 基础样式和变量
  - `components/` - 组件样式（预留）
  - `utilities/` - 工具类样式

### 文件拆分
- `src/styles/base/variables.css` - CSS自定义变量系统，包含颜色、尺寸等主题配置
- `src/styles/base/base.css` - 基础元素样式和全局重置规则
- `src/styles/utilities/utilities.css` - 自定义工具类（如 text-balance）
- `src/styles/globals.css` - 全局样式入口文件，导入所有模块

### 引用路径更新
- 修改 `src/main.tsx` 中的导入路径：`import './styles/globals.css'`
- 删除原有的 `src/globals.css` 文件

## layout.tsx 文件位置问题

### 当前问题
`layout.tsx` 文件当前位于 `src` 目录下。根据 Next.js 的约定，布局文件应放置在 `src/app` 目录结构中，以正确定义不同路由的布局。

### 推荐解决方案
1. 如果 `src/app` 目录不存在，则创建该目录。
2. 将 `layout.tsx` 文件从 `src` 目录移动到 `src/app` 目录。
3. 确保布局文件遵循 Next.js App Router 约定来定义根布局。

### 实施步骤
1. 创建 `src/app` 目录（如果不存在）。
2. 将 `src/layout.tsx` 移动到 `src/app/layout.tsx`。
3. 验证布局文件是否正确地为应用程序定义了根布局。

### 好处
- 符合 Next.js 最佳实践
- 改善项目结构组织
- 确保正确的路由和布局管理

## 技术特性
- 支持明/暗主题切换的CSS变量系统
- 符合Tailwind CSS的层级结构（base, components, utilities）
- 模块化设计，便于后续扩展和维护
- 保持与原有功能的完全兼容

## 执行时间
2025年1月（具体日期）

## 相关文档
- 项目结构文档已更新：<mcfile name="PROJECT_STRUCTURE.md" path="c:\Users\<USER>\Documents\retal\docs\PROJECT_STRUCTURE.md"></mcfile>