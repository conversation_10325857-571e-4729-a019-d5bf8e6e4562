# 停车管理模块设计文档

## 整体架构图

```mermaid
graph TD
    A[前端UI层] --> B[API服务层]
    B --> C[后端路由层]
    C --> D[数据库访问层]
    D --> E[(数据库)]
    
    subgraph 前端
        A
    end
    
    subgraph 后端
        B
        C
        D
        E
    end
    
    F[认证模块] -.-> C
    G[仪表板模块] --> B
```

## 分层设计和核心组件

### 前端组件
1. `ParkingPage` - 车位管理主页面
2. `ParkingList` - 车位列表组件
3. `ParkingForm` - 车位表单组件
4. `ParkingDetail` - 车位详情组件
5. `ParkingFormPage` - 车位表单页面
6. `ParkingDetailPage` - 车位详情页面

### 后端组件
1. `parking.py` - 车位管理路由
2. `leases.py` - 租赁合同管理路由
3. `models.py` - 数据模型定义
4. `schemas.py` - 数据模式定义

## 模块依赖关系图

```mermaid
graph TD
    A[ParkingPage] --> B[ParkingList]
    A --> C[ParkingFormPage]
    A --> D[ParkingDetailPage]
    
    B --> E[ApiService]
    C --> F[ParkingForm]
    F --> E
    D --> G[ParkingDetail]
    G --> E
    
    E --> H[后端API]
    
    subgraph 前端组件
        A
        B
        C
        D
        F
        G
    end
    
    subgraph 服务层
        E
    end
    
    subgraph 后端
        H
    end
```

## 接口契约定义

### 前端API接口
1. `getParkingSpaces()` - 获取车位列表
2. `getParkingSpace(id)` - 获取车位详情
3. `createParkingSpace(data)` - 创建车位
4. `updateParkingSpace(id, data)` - 更新车位
5. `deleteParkingSpace(id)` - 删除车位
6. `getParkingStatistics()` - 获取车位统计数据

### 后端API接口
1. `GET /parking/` - 获取车位列表
2. `GET /parking/{id}` - 获取车位详情
3. `POST /parking/` - 创建车位
4. `PUT /parking/{id}` - 更新车位
5. `DELETE /parking/{id}` - 删除车位
6. `GET /parking/statistics/overview` - 获取车位统计数据
7. `POST /leases/` - 创建租赁合同

## 数据流向图

```mermaid
graph LR
    A[用户操作] --> B[前端组件]
    B --> C[API服务]
    C --> D[后端路由]
    D --> E[数据库]
    E --> D
    D --> C
    C --> B
    B --> F[UI更新]
```

## 异常处理策略
1. 前端网络请求异常显示错误提示
2. 后端数据库操作异常记录日志并返回错误信息
3. 权限验证失败返回401或403状态码
4. 数据验证失败返回422状态码
5. 服务器内部错误返回500状态码

## SQLAlchemy重构设计

### 重构前设计
1. parking.py中的create_parking_space函数使用原生SQL插入车位数据
2. leases.py中的create_lease函数使用原生SQL创建租赁合同

### 重构后设计
1. parking.py中的create_parking_space函数使用SQLAlchemy ORM创建车位
2. leases.py中的create_lease函数使用SQLAlchemy ORM创建租赁合同
3. 利用SQLAlchemy的session管理确保事务一致性
4. 保持异常处理机制不变