import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Plus, ArrowUpRight, ArrowDownRight, Calendar, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import ApiService from '@/services/api';

const TransactionList = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [dateFilter, setDateFilter] = useState('本月');
  const [transactions, setTransactions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();

  // 获取交易列表数据
  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setIsLoading(true);
        // 根据日期筛选构建查询参数
        const params: any = {};
        if (dateFilter === '本月') {
          const now = new Date();
          const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
          params.start_date = firstDay.toISOString().split('T')[0];
        } else if (dateFilter === '上月') {
          const now = new Date();
          const firstDay = new Date(now.getFullYear(), now.getMonth() - 1, 1);
          const lastDay = new Date(now.getFullYear(), now.getMonth(), 0);
          params.start_date = firstDay.toISOString().split('T')[0];
          params.end_date = lastDay.toISOString().split('T')[0];
        }
        
        const data = await ApiService.getTransactions(params);
        setTransactions(data);
        setError(null);
      } catch (err: any) {
        console.error('获取交易列表失败:', err);
        setError('获取交易列表失败，请稍后重试');
        toast({
          title: '数据加载失败',
          description: err.message || '无法加载交易数据，请检查网络连接',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchTransactions();
  }, [dateFilter, toast]);

  // 根据搜索词过滤交易
  const filteredTransactions = transactions.filter(transaction => 
    transaction.description?.toLowerCase().includes(searchTerm.toLowerCase()) || 
    transaction.property?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.tenant?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 根据标签过滤交易
  const getFilteredTransactionsByTab = () => {
    if (activeTab === 'all') return filteredTransactions;
    if (activeTab === 'income') return filteredTransactions.filter(transaction => transaction.type === '收入');
    if (activeTab === 'expense') return filteredTransactions.filter(transaction => transaction.type === '支出');
    return filteredTransactions;
  };

  // 获取类型对应的颜色
  const getTypeColor = (type: string) => {
    switch (type) {
      case '收入':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case '支出':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  // 获取类型对应的图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case '收入':
        return <ArrowUpRight className="h-4 w-4 text-green-600" />;
      case '支出':
        return <ArrowDownRight className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  const displayTransactions = getFilteredTransactionsByTab();

  return (
    <div className="space-y-4">
      {/* 搜索和筛选区 */}
      <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="搜索交易..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex space-x-2">
          <Select 
            value={dateFilter} 
            onValueChange={setDateFilter}
          >
            <SelectTrigger className="w-[120px]">
              <Calendar className="h-4 w-4 mr-2" />
              <SelectValue placeholder="选择时间" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="本月">本月</SelectItem>
              <SelectItem value="上月">上月</SelectItem>
              <SelectItem value="本季度">本季度</SelectItem>
              <SelectItem value="本年">本年</SelectItem>
              <SelectItem value="自定义">自定义</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
            <span className="sr-only">筛选</span>
          </Button>
          <Button
            className="bg-amber-600 hover:bg-amber-700"
            onClick={() => navigate('/finance/add')}
          >
            <Plus className="h-4 w-4 mr-2" />
            添加交易
          </Button>
        </div>
      </div>

      {/* 类型选项卡 */}
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all">全部</TabsTrigger>
          <TabsTrigger value="income">收入</TabsTrigger>
          <TabsTrigger value="expense">支出</TabsTrigger>
        </TabsList>
        
        {/* 交易列表内容 */}
        <TabsContent value={activeTab} className="space-y-4 mt-4">
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-amber-600" />
              <span className="ml-2 text-lg text-gray-600">加载中...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-500">{error}</p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => window.location.reload()}
              >
                重试
              </Button>
            </div>
          ) : displayTransactions.length > 0 ? (
            displayTransactions.map((transaction) => (
              <Card 
                key={transaction.id} 
                className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => navigate(`/finance/${transaction.id}`)}
              >
                <CardContent className="p-0">
                  <div className="flex items-center p-4">
                    <div className={`h-10 w-10 rounded-full flex items-center justify-center mr-4 ${transaction.type === '收入' ? 'bg-green-100' : 'bg-red-100'}`}>
                      {getTypeIcon(transaction.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold">{transaction.description}</h3>
                        <div className="flex items-center">
                          <span className={`text-lg font-bold ${transaction.type === '收入' ? 'text-green-600' : 'text-red-600'}`}>
                            {transaction.type === '收入' ? '+' : '-'}¥{transaction.amount}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <div className="flex items-center text-gray-500">
                          <Badge className={getTypeColor(transaction.type)}>
                            {transaction.category}
                          </Badge>
                          {transaction.property && (
                            <span className="ml-2 text-sm">{transaction.property}</span>
                          )}
                          {transaction.tenant && (
                            <span className="ml-2 text-sm">({transaction.tenant})</span>
                          )}
                        </div>
                        <span className="text-sm text-gray-500">{transaction.date}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">没有找到匹配的交易记录</p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TransactionList;