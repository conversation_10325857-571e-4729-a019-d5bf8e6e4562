"""
租房管理系统后端主入口
用于启动FastAPI应用服务器
"""
import sys
import logging
import uvicorn

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('Main')

def main():
    """主函数，启动应用服务器"""
    try:
        logger.info("正在启动租房管理系统后端服务...")
        
        # 启动FastAPI应用
        uvicorn.run(
            "app.main:app", 
            host="0.0.0.0", 
            port=8000, 
            reload=True,
            log_level="info"
        )
    except Exception as e:
        logger.error(f"启动应用服务器时出错: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())