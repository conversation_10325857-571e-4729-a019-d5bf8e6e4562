from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime
from typing import Annotated
from ..database import get_db
from ..models import Maintenance, Property, Tenant, User
from ..schemas import Maintenance as MaintenanceSchema, MaintenanceCreate, MaintenanceUpdate
from .auth import get_current_active_user

router = APIRouter(
    prefix="/maintenance",
    tags=["维修管理"],
    responses={404: {"description": "未找到"}},
)

# 获取所有维修请求
@router.get("/", response_model=list[MaintenanceSchema])
def read_maintenance_requests(
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    skip: int = 0, 
    limit: int = 100, 
    property_id: int | None = None,
    tenant_id: int | None = None,
    status: str | None = None,
    priority: str | None = None
):
    query = db.query(Maintenance).join(Property).filter(Property.owner_id == current_user.id)
    
    if property_id:
        query = query.filter(Maintenance.property_id == property_id)
    
    if tenant_id:
        query = query.filter(Maintenance.tenant_id == tenant_id)
    
    if status:
        query = query.filter(Maintenance.status == status)
    
    if priority:
        query = query.filter(Maintenance.priority == priority)
    
    maintenance_requests = query.order_by(Maintenance.reported_date.desc()).offset(skip).limit(limit).all()
    return maintenance_requests

# 创建维修请求
@router.post("/", response_model=MaintenanceSchema, status_code=status.HTTP_201_CREATED)
def create_maintenance_request(
    maintenance_data: MaintenanceCreate, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    # 验证房屋是否存在且属于当前用户
    property = db.query(Property).filter(
        Property.id == maintenance_data.property_id,
        Property.owner_id == current_user.id
    ).first()
    
    if not property:
        raise HTTPException(status_code=404, detail="指定的房屋不存在或不属于当前用户")
    
    # 如果指定了租客，验证租客是否存在且与该房屋关联
    if maintenance_data.tenant_id:
        tenant = db.query(Tenant).filter(
            Tenant.id == maintenance_data.tenant_id,
            Tenant.property_id == maintenance_data.property_id
        ).first()
        
        if not tenant:
            raise HTTPException(status_code=404, detail="指定的租客不存在或不与该房屋关联")
    
    db_maintenance = Maintenance(**maintenance_data.model_dump())
    db.add(db_maintenance)  # type: ignore[unknown-member-type]
    db.commit()  # type: ignore[unknown-member-type]
    db.refresh(db_maintenance)  # type: ignore[unknown-member-type]
    return db_maintenance

# 获取单个维修请求
@router.get("/{maintenance_id}", response_model=MaintenanceSchema)
def read_maintenance_request(
    maintenance_id: int, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    maintenance = db.query(Maintenance).join(Property).filter(
        Maintenance.id == maintenance_id,
        Property.owner_id == current_user.id
    ).first()
    
    if maintenance is None:
        raise HTTPException(status_code=404, detail="维修请求未找到")
    return maintenance

# 更新维修请求
@router.put("/{maintenance_id}", response_model=MaintenanceSchema)
def update_maintenance_request(
    maintenance_id: int, 
    maintenance_data: MaintenanceUpdate, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    # 验证房屋是否存在且属于当前用户
    property = db.query(Property).filter(
        Property.id == maintenance_data.property_id,
        Property.owner_id == current_user.id
    ).first()
    
    if not property:
        raise HTTPException(status_code=404, detail="指定的房屋不存在或不属于当前用户")
    
    # 如果指定了租客，验证租客是否存在且与该房屋关联
    if maintenance_data.tenant_id:
        tenant = db.query(Tenant).filter(
            Tenant.id == maintenance_data.tenant_id,
            Tenant.property_id == maintenance_data.property_id
        ).first()
        
        if not tenant:
            raise HTTPException(status_code=404, detail="指定的租客不存在或不与该房屋关联")
    
    maintenance = db.query(Maintenance).join(Property).filter(
        Maintenance.id == maintenance_id,
        Property.owner_id == current_user.id
    ).first()
    
    if maintenance is None:
        raise HTTPException(status_code=404, detail="维修请求未找到")
    
    update_data = maintenance_data.model_dump(exclude_unset=True)
    if 'property_id' in update_data:
        maintenance.property_id = update_data['property_id']
    if 'tenant_id' in update_data:
        maintenance.tenant_id = update_data['tenant_id']
    if 'title' in update_data:
        maintenance.title = update_data['title']
    if 'description' in update_data:
        maintenance.description = update_data['description']
    if 'priority' in update_data:
        maintenance.priority = update_data['priority']
    if 'status' in update_data:
        maintenance.status = update_data['status']
    
    db.commit()
    db.refresh(maintenance)
    return maintenance

# 删除维修请求
@router.delete("/{maintenance_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_maintenance_request(
    maintenance_id: int, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    maintenance = db.query(Maintenance).join(Property).filter(
        Maintenance.id == maintenance_id,
        Property.owner_id == current_user.id
    ).first()
    
    if maintenance is None:
        raise HTTPException(status_code=404, detail="维修请求未找到")
    
    db.delete(maintenance)
    db.commit()
    return {"detail": "维修请求已删除"}

# 更新维修状态
@router.patch("/{maintenance_id}/status", response_model=MaintenanceSchema)
def update_maintenance_status(
    maintenance_id: int, 
    status: str,
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    from ..models import MaintenanceStatus
    
    # 验证状态值是否有效
    try:
        maintenance_status = MaintenanceStatus(status)
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的维修状态")
    
    maintenance = db.query(Maintenance).join(Property).filter(
        Maintenance.id == maintenance_id,
        Property.owner_id == current_user.id
    ).first()
    
    if maintenance is None:
        raise HTTPException(status_code=404, detail="维修请求未找到")
    
    maintenance.status = maintenance_status
    
    # 如果状态为已完成，自动设置完成日期
    if maintenance_status == MaintenanceStatus.COMPLETED:
        maintenance.completed_date = datetime.now()
    
    db.commit()
    db.refresh(maintenance)
    return maintenance