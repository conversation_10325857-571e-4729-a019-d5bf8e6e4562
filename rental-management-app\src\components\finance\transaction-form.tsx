import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { FileText, Home, Car } from 'lucide-react';
import ApiService from '@/services/api';

interface TransactionFormProps {
  editMode?: boolean;
  transactionData?: any;
}

interface Contract {
  id: number;
  tenant_id: number;
  property_id?: number;
  parking_space_id?: number;
  lease_type: 'property' | 'parking';
  monthly_rent: number;
  payment_method: string;
  status: string;
  tenant?: {
    name: string;
  };
  property?: {
    name: string;
  };
  parking_space?: {
    name: string;
  };
}

const TransactionForm = ({ editMode = false, transactionData = null }: TransactionFormProps) => {
  const [searchParams] = useSearchParams();
  const [formData, setFormData] = useState({
    type: editMode && transactionData ? transactionData.type : '收入',
    category: editMode && transactionData ? transactionData.category : '',
    amount: editMode && transactionData ? transactionData.amount : '',
    date: editMode && transactionData ? transactionData.date : new Date().toISOString().split('T')[0],
    propertyId: editMode && transactionData ? transactionData.propertyId : 'none',
    tenantId: editMode && transactionData ? transactionData.tenantId : 'none',
    contractId: editMode && transactionData ? transactionData.contractId : 'none',
    description: editMode && transactionData ? transactionData.description : '',
    isRentPayment: false, // 是否为租金收款
  });

  const [isLoading, setIsLoading] = useState(false);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [selectedContract, setSelectedContract] = useState<Contract | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();

  // 获取生效中的合同数据
  useEffect(() => {
    const fetchContracts = async () => {
      try {
        const contractsData = await ApiService.getLeases({ status: '生效中' });
        setContracts(contractsData || []);
      } catch (error) {
        console.error('获取合同数据失败:', error);
      }
    };

    fetchContracts();
  }, []);

  // 处理URL参数预填充
  useEffect(() => {
    const type = searchParams.get('type');
    const category = searchParams.get('category');
    const amount = searchParams.get('amount');
    const description = searchParams.get('description');
    const contractId = searchParams.get('contract_id');

    if (type || category || amount || description || contractId) {
      setFormData(prev => ({
        ...prev,
        type: type || prev.type,
        category: category || prev.category,
        amount: amount || prev.amount,
        description: description || prev.description,
        contractId: contractId || prev.contractId,
        isRentPayment: category === '租金收入'
      }));

      // 如果有合同ID，获取合同详情
      if (contractId) {
        const contract = contracts.find(c => c.id.toString() === contractId);
        if (contract) {
          setSelectedContract(contract);
        }
      }
    }
  }, [searchParams, contracts]);

  // 收入和支出类别
  const incomeCategories = ['租金收入', '押金收入', '其他收入'];
  const expenseCategories = ['维修费用', '物业费', '水电费', '税费', '保险费', '中介费', '装修费', '其他支出'];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleTypeChange = (value: string) => {
    setFormData({
      ...formData,
      type: value,
      category: '', // 重置类别
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 表单验证
    if (!formData.category || !formData.amount || !formData.date) {
      toast({
        title: '输入错误',
        description: '请填写所有必填字段',
        variant: 'destructive',
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      // 准备提交数据，确保tenantId为null（支出方式中不需要关联租客信息）
      const submitData = {
        ...formData,
        tenantId: null, // 租客信息不再需要关联
        amount: parseFloat(formData.amount),
        date: new Date(formData.date).toISOString()
      };
      
      if (editMode && transactionData) {
        // 更新交易
        await ApiService.updateTransaction(transactionData.id, submitData);
        toast({
          title: '更新成功',
          description: '交易记录已更新',
        });
      } else {
        // 创建新交易
        await ApiService.createTransaction(submitData);
        toast({
          title: '添加成功',
          description: '新交易记录已添加到系统',
        });
      }
      
      navigate('/finance');
    } catch (error) {
      console.error('提交交易失败:', error);
      toast({
        title: '提交失败',
        description: '交易记录提交失败，请重试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-gray-50 pb-16">
      <form onSubmit={handleSubmit} className="p-4 space-y-6">
        <Card className="shadow-lg border-0">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold text-gray-800">
              {editMode ? '编辑交易' : '添加交易'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 交易类型 */}
            <div className="space-y-3">
              <Label className="text-base font-semibold text-gray-700">交易类型 *</Label>
              <RadioGroup
                defaultValue={formData.type}
                className="grid grid-cols-2 gap-3"
                onValueChange={handleTypeChange}
              >
                <div className="flex items-center space-x-3 p-4 border-2 border-green-200 rounded-lg bg-green-50 hover:bg-green-100 transition-colors">
                  <RadioGroupItem value="收入" id="income" className="text-green-600" />
                  <Label htmlFor="income" className="text-green-700 font-semibold text-base cursor-pointer flex-1">
                    💰 收入
                  </Label>
                </div>
                <div className="flex items-center space-x-3 p-4 border-2 border-red-200 rounded-lg bg-red-50 hover:bg-red-100 transition-colors">
                  <RadioGroupItem value="支出" id="expense" className="text-red-600" />
                  <Label htmlFor="expense" className="text-red-700 font-semibold text-base cursor-pointer flex-1">
                    💸 支出
                  </Label>
                </div>
              </RadioGroup>
            </div>
            
            {/* 交易类别 */}
            <div className="space-y-3">
              <Label htmlFor="category" className="text-base font-semibold text-gray-700">交易类别 *</Label>
              <Select
                value={formData.category}
                onValueChange={(value) => handleSelectChange('category', value)}
              >
                <SelectTrigger className="h-12 text-base border-2 border-gray-200 focus:border-blue-500">
                  <SelectValue placeholder="请选择交易类别" />
                </SelectTrigger>
                <SelectContent>
                  {formData.type === '收入' ? (
                    incomeCategories.map((category, index) => (
                      <SelectItem key={index} value={category} className="text-base py-3">
                        {category}
                      </SelectItem>
                    ))
                  ) : (
                    expenseCategories.map((category, index) => (
                      <SelectItem key={index} value={category} className="text-base py-3">
                        {category}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* 租金收款 - 合同选择 */}
            {formData.category === '租金收入' && (
              <div className="space-y-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border-2 border-blue-200">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <FileText className="h-4 w-4 text-white" />
                  </div>
                  <Label className="text-blue-800 font-bold text-lg">🏠 租金收款</Label>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="contractId" className="text-base font-semibold text-gray-700">选择合同 *</Label>
                  <Select
                    value={formData.contractId}
                    onValueChange={(value) => {
                      handleSelectChange('contractId', value);
                      const contract = contracts.find(c => c.id.toString() === value);
                      setSelectedContract(contract || null);
                      if (contract) {
                        // 根据付款方式计算应收租金
                        const calculateRentAmount = (monthlyRent: number, paymentMethod: string) => {
                          switch (paymentMethod) {
                            case '押一付一': return monthlyRent;
                            case '押一付三': return monthlyRent * 3;
                            case '押一付六': return monthlyRent * 6;
                            case '押一付十二': return monthlyRent * 12;
                            case '半年付': return monthlyRent * 6;
                            case '年付': return monthlyRent * 12;
                            default: return monthlyRent;
                          }
                        };

                        const rentAmount = calculateRentAmount(contract.monthly_rent, contract.payment_method);

                        setFormData(prev => ({
                          ...prev,
                          amount: rentAmount.toString(),
                          description: `${contract.tenant?.name || '租客'} - ${contract.lease_type === 'property' ? '房屋' : '车位'}租金 (${contract.payment_method})`
                        }));
                      }
                    }}
                  >
                    <SelectTrigger className="h-12 text-base border-2 border-blue-200 focus:border-blue-500">
                      <SelectValue placeholder="请选择需要收款的合同" />
                    </SelectTrigger>
                    <SelectContent className="max-h-60 w-full">
                      {contracts.map((contract) => (
                        <SelectItem key={contract.id} value={contract.id.toString()} className="py-2">
                          <div className="flex items-center justify-between w-full min-w-0">
                            <div className="flex items-center space-x-2 flex-1 min-w-0">
                              {contract.lease_type === 'property' ? (
                                <Home className="h-4 w-4 text-blue-500 flex-shrink-0" />
                              ) : (
                                <Car className="h-4 w-4 text-purple-500 flex-shrink-0" />
                              )}
                              <div className="min-w-0 flex-1">
                                <div className="font-medium truncate">{contract.tenant?.name || '未知租客'}</div>
                                <div className="text-xs text-gray-500 truncate">
                                  {contract.property?.name || contract.parking_space?.name || '未知房屋'}
                                </div>
                              </div>
                            </div>
                            <Badge variant="outline" className="text-green-600 border-green-200 ml-2 flex-shrink-0">
                              ¥{contract.monthly_rent}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 选中合同的详细信息 */}
                {selectedContract && (
                  <div className="bg-white rounded-xl border-2 border-gray-100 shadow-sm space-y-4 p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <span className="text-gray-600 font-medium">👤 租客</span>
                        <span className="font-bold text-gray-800">{selectedContract.tenant?.name || '未知'}</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <span className="text-gray-600 font-medium">💰 月租金</span>
                        <span className="font-bold text-green-600">¥{selectedContract.monthly_rent}</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <span className="text-gray-600 font-medium">📋 付款方式</span>
                        <span className="font-bold text-blue-600">{selectedContract.payment_method}</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                        <span className="text-gray-600 font-medium">✅ 合同状态</span>
                        <Badge className="bg-green-100 text-green-700 border-green-200">
                          {selectedContract.status}
                        </Badge>
                      </div>
                    </div>

                    {/* 应收租金计算 */}
                    <div className="border-t-2 border-dashed border-gray-200 pt-4">
                      <div className="bg-gradient-to-r from-orange-50 to-yellow-50 p-4 rounded-xl border border-orange-200">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-orange-700 font-bold text-sm">💸 本期应收</span>
                            <span className="text-xl font-bold text-orange-600 whitespace-nowrap">
                              ¥{(() => {
                                const monthlyRent = selectedContract.monthly_rent;
                                const paymentMethod = selectedContract.payment_method;

                                // 根据付款方式计算应收金额
                                switch (paymentMethod) {
                                  case '押一付一': return monthlyRent;
                                  case '押一付三': return monthlyRent * 3;
                                  case '押一付六': return monthlyRent * 6;
                                  case '押一付十二': return monthlyRent * 12;
                                  case '半年付': return monthlyRent * 6;
                                  case '年付': return monthlyRent * 12;
                                  default: return monthlyRent;
                                }
                              })()}
                            </span>
                          </div>
                          <div className="text-xs text-orange-600 font-medium">
                            📊 {selectedContract.payment_method} 自动计算
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 金额 */}
            <div className="space-y-3">
              <Label htmlFor="amount" className="text-base font-semibold text-gray-700 flex items-center">
                💰 金额 (元) *
              </Label>
              <div className="relative">
                <Input
                  id="amount"
                  name="amount"
                  type="number"
                  placeholder="请输入金额"
                  value={formData.amount}
                  onChange={handleInputChange}
                  required
                  disabled={formData.category === '租金收入' && !!selectedContract}
                  className="h-12 text-lg font-semibold border-2 border-gray-200 focus:border-blue-500 pl-8"
                />
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">¥</span>
              </div>
              {formData.category === '租金收入' && selectedContract && (
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <p className="text-sm text-blue-700 font-medium">
                    💡 金额已根据合同自动填充，如需部分收款可点击输入框修改
                  </p>
                </div>
              )}
            </div>

            {/* 日期 */}
            <div className="space-y-3">
              <Label htmlFor="date" className="text-base font-semibold text-gray-700 flex items-center">
                📅 交易日期 *
              </Label>
              <Input
                id="date"
                name="date"
                type="date"
                value={formData.date}
                onChange={handleInputChange}
                required
                className="h-12 text-base border-2 border-gray-200 focus:border-blue-500"
              />
            </div>
            
            {/* 关联信息 - 对于租金收入，这些信息已通过合同自动关联 */}
            {formData.category !== '租金收入' && (
              <>
                {/* 关联房屋 */}
                <div className="space-y-2">
                  <Label htmlFor="propertyId">关联房屋</Label>
                  <Select
                    value={formData.propertyId}
                    onValueChange={(value) => handleSelectChange('propertyId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择房屋（可选）" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">无</SelectItem>
                      {/* 这里可以添加房屋选项，暂时简化 */}
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}
            
            {/* 描述 */}
            <div className="space-y-3">
              <Label htmlFor="description" className="text-base font-semibold text-gray-700 flex items-center">
                📝 交易描述
              </Label>
              <Textarea
                id="description"
                name="description"
                placeholder="请输入交易描述（可选）"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="text-base border-2 border-gray-200 focus:border-blue-500 resize-none"
              />
            </div>
          </CardContent>
        </Card>

        {/* 提交按钮 - 固定在底部 */}
        <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t shadow-lg z-50">
          <div className="max-w-md mx-auto flex space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/finance')}
              className="flex-1 h-12 text-base font-semibold border-2 border-gray-300 hover:border-gray-400"
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className={`flex-1 h-12 text-base font-semibold shadow-lg transition-all duration-200 ${
                formData.type === '收入'
                  ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700'
                  : 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700'
              }`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  保存中...
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <span className="mr-2">{formData.type === '收入' ? '💰' : '💸'}</span>
                  {editMode ? '更新交易' : '添加交易'}
                </div>
              )}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default TransactionForm;