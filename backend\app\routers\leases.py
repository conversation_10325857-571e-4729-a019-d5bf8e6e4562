# Copyright (c) 2025 云福阁. All rights reserved.
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import text
from typing import Annotated
import logging
from ..database import get_db
from ..models import Lease, Tenant, Property, ParkingSpace, User
from ..schemas import (
    Lease as LeaseSchema,
    LeaseCreate,
    LeaseUpdate
)
from .auth import get_current_active_user

# 配置日志
logger = logging.getLogger('Leases')

router = APIRouter(
    prefix="/leases",
    tags=["租赁合同管理"],
    responses={404: {"description": "未找到"}},
)

# 获取租赁合同列表
@router.get("/", response_model=list[LeaseSchema])
def read_leases(
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    skip: int = 0, 
    limit: int = 100,
    lease_type: str | None = None,
    status: str | None = None,
    tenant_id: int | None = None
):
    try:
        logger.info(f"用户 {current_user.username} 请求获取租赁合同列表")

        # 简化查询逻辑，先检查是否有租赁合同数据
        total_leases = db.query(Lease).count()
        logger.info(f"数据库中总共有 {total_leases} 个租赁合同")

        if total_leases == 0:
            logger.info("数据库中没有租赁合同数据，返回空列表")
            return []

        # 构建查询，确保只能访问自己的房屋或车位的租赁合同
        if lease_type == 'property':
            # 只查询房屋租赁合同
            leases = db.query(Lease).options(
                joinedload(Lease.tenant),
                joinedload(Lease.property)
            ).filter(
                Lease.lease_type == 'property',
                Lease.property_id.in_(
                    db.query(Property.id).filter(Property.owner_id == current_user.id)
                )
            ).order_by(Lease.created_at.desc()).offset(skip).limit(limit).all()
        elif lease_type == 'parking':
            # 只查询车位租赁合同
            leases = db.query(Lease).options(
                joinedload(Lease.tenant),
                joinedload(Lease.parking_space)
            ).filter(
                Lease.lease_type == 'parking',
                Lease.parking_space_id.in_(
                    db.query(ParkingSpace.id).filter(ParkingSpace.owner_id == current_user.id)
                )
            ).order_by(Lease.created_at.desc()).offset(skip).limit(limit).all()
        else:
            # 获取所有类型的合同
            property_lease_ids = db.query(Lease.id).filter(
                Lease.lease_type == 'property',
                Lease.property_id.in_(
                    db.query(Property.id).filter(Property.owner_id == current_user.id)
                )
            )
            parking_lease_ids = db.query(Lease.id).filter(
                Lease.lease_type == 'parking',
                Lease.parking_space_id.in_(
                    db.query(ParkingSpace.id).filter(ParkingSpace.owner_id == current_user.id)
                )
            )

            all_lease_ids = property_lease_ids.union(parking_lease_ids)
            leases = db.query(Lease).options(
                joinedload(Lease.tenant),
                joinedload(Lease.property),
                joinedload(Lease.parking_space)
            ).filter(
                Lease.id.in_(all_lease_ids)
            ).order_by(Lease.created_at.desc()).offset(skip).limit(limit).all()

        # 添加其他过滤条件
        if status:
            leases = [lease for lease in leases if lease.status == status]
        if tenant_id:
            leases = [lease for lease in leases if lease.tenant_id == tenant_id]

        logger.info(f"返回 {len(leases)} 个租赁合同")
        return leases

    except Exception as e:
        logger.error(f"获取租赁合同列表失败: {str(e)}")
        logger.error(f"错误详情: {type(e).__name__}: {str(e)}")
        import traceback
        logger.error(f"堆栈跟踪: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取租赁合同列表失败: {str(e)}"
        )

# 创建租赁合同
@router.post("/", response_model=LeaseSchema, status_code=status.HTTP_201_CREATED)
def create_lease(
    lease_data: LeaseCreate, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    try:
        logger.info(f"用户 {current_user.username} 请求创建租赁合同")
        logger.info(f"合同数据: {lease_data.model_dump()}")
        
        # 验证租客是否存在
        tenant = db.query(Tenant).filter(Tenant.id == lease_data.tenant_id).first()
        if not tenant:
            raise HTTPException(status_code=404, detail="指定的租客不存在")
        
        # 根据租赁类型验证房屋或车位
        if lease_data.lease_type == 'property':
            if not lease_data.property_id:
                raise HTTPException(status_code=400, detail="房屋租赁必须指定房屋ID")
            
            property = db.query(Property).filter(
                Property.id == lease_data.property_id,
                Property.owner_id == current_user.id
            ).first()
            if not property:
                raise HTTPException(status_code=404, detail="指定的房屋不存在或不属于当前用户")
                
        elif lease_data.lease_type == 'parking':
            if not lease_data.parking_space_id:
                raise HTTPException(status_code=400, detail="车位租赁必须指定车位ID")
            if not lease_data.car_number:
                raise HTTPException(status_code=400, detail="车位租赁必须提供车牌号")
                
            parking_space = db.query(ParkingSpace).filter(
                ParkingSpace.id == lease_data.parking_space_id,
                ParkingSpace.owner_id == current_user.id
            ).first()
            if not parking_space:
                raise HTTPException(status_code=404, detail="指定的车位不存在或不属于当前用户")
        else:
            raise HTTPException(status_code=400, detail="无效的租赁类型")
        
        # 使用SQLAlchemy ORM创建租赁合同
        lease_dict = lease_data.model_dump()
        db_lease = Lease(**lease_dict)
        db.add(db_lease)
        db.commit()
        db.refresh(db_lease)
        
        logger.info(f"租赁合同创建成功，ID: {db_lease.id}")
        return db_lease
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建租赁合同失败: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建租赁合同失败: {str(e)}"
        )

# 获取单个租赁合同
@router.get("/{lease_id}", response_model=LeaseSchema)
def read_lease(
    lease_id: int, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    try:
        logger.info(f"用户 {current_user.username} 请求获取租赁合同详情，lease_id={lease_id}")
        
        # 查询租赁合同并验证权限
        lease = db.query(Lease).options(
            joinedload(Lease.tenant),
            joinedload(Lease.property),
            joinedload(Lease.parking_space)
        ).filter(Lease.id == lease_id).first()
        if not lease:
            raise HTTPException(status_code=404, detail="租赁合同未找到")
        
        # 验证权限
        if lease.lease_type == 'property':
            property = db.query(Property).filter(Property.id == lease.property_id).first()
            if not property or property.owner_id != current_user.id:
                raise HTTPException(status_code=403, detail="无权访问此租赁合同")
        elif lease.lease_type == 'parking':
            parking_space = db.query(ParkingSpace).filter(ParkingSpace.id == lease.parking_space_id).first()
            if not parking_space or parking_space.owner_id != current_user.id:
                raise HTTPException(status_code=403, detail="无权访问此租赁合同")
        
        logger.info(f"成功获取租赁合同 {lease_id} 的详情")
        return lease
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取租赁合同详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取租赁合同详情失败: {str(e)}"
        )

# 更新租赁合同
@router.put("/{lease_id}", response_model=LeaseSchema)
def update_lease(
    lease_id: int, 
    lease_data: LeaseUpdate, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    try:
        logger.info(f"用户 {current_user.username} 请求更新租赁合同，lease_id={lease_id}")
        logger.info(f"更新数据: {lease_data.model_dump(exclude_unset=True)}")
        
        # 查询租赁合同并验证权限
        lease = db.query(Lease).filter(Lease.id == lease_id).first()
        if not lease:
            raise HTTPException(status_code=404, detail="租赁合同未找到")
        
        # 验证权限
        if lease.lease_type == 'property':
            property = db.query(Property).filter(Property.id == lease.property_id).first()
            if not property or property.owner_id != current_user.id:
                raise HTTPException(status_code=403, detail="无权访问此租赁合同")
        elif lease.lease_type == 'parking':
            parking_space = db.query(ParkingSpace).filter(ParkingSpace.id == lease.parking_space_id).first()
            if not parking_space or parking_space.owner_id != current_user.id:
                raise HTTPException(status_code=403, detail="无权访问此租赁合同")
        
        # 更新合同信息
        for key, value in lease_data.model_dump(exclude_unset=True).items():
            setattr(lease, key, value)
        
        db.commit()
        db.refresh(lease)
        
        logger.info(f"租赁合同 {lease_id} 更新成功")
        return lease
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新租赁合同失败: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新租赁合同失败: {str(e)}"
        )

# 删除租赁合同
@router.delete("/{lease_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_lease(
    lease_id: int, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    try:
        logger.info(f"用户 {current_user.username} 请求删除租赁合同，lease_id={lease_id}")
        
        # 查询租赁合同并验证权限
        lease = db.query(Lease).filter(Lease.id == lease_id).first()
        if not lease:
            raise HTTPException(status_code=404, detail="租赁合同未找到")
        
        # 验证权限
        if lease.lease_type == 'property':
            property = db.query(Property).filter(Property.id == lease.property_id).first()
            if not property or property.owner_id != current_user.id:
                raise HTTPException(status_code=403, detail="无权访问此租赁合同")
        elif lease.lease_type == 'parking':
            parking_space = db.query(ParkingSpace).filter(ParkingSpace.id == lease.parking_space_id).first()
            if not parking_space or parking_space.owner_id != current_user.id:
                raise HTTPException(status_code=403, detail="无权访问此租赁合同")
        
        db.delete(lease)
        db.commit()
        
        logger.info(f"租赁合同 {lease_id} 删除成功")
        return {"message": "租赁合同删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除租赁合同失败: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除租赁合同失败: {str(e)}"
        )
