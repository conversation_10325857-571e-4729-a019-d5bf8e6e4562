# Copyright (c) 2025 Aonis. All rights reserved.

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from starlette.responses import Response
from .routers import auth, properties, tenants, transactions, maintenance, notifications, upload, leases
from .routers import parking
from .init_db import check_connection
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('API')

# 检查数据库连接
if check_connection():
    logger.info("数据库连接正常，应用启动成功")
else:
    logger.error("无法连接到数据库，应用可能无法正常工作")

# 创建FastAPI应用
app = FastAPI(
    title="租房管理系统API",
    description="用于租房管理系统的后端API",
    version="1.0.0"
)

# 配置CORS - 允许局域网访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://*************:5173",  # 局域网前端地址
        "http://*************:5173",  # 可能的其他设备地址
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 自定义StaticFiles类以禁用缓存
class NoCacheStaticFiles(StaticFiles):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
    
    def file_response(self, *args, **kwargs):
        response = super().file_response(*args, **kwargs)
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        return response

# 配置静态文件服务（必须在API路由之前，但API路由会优先匹配）
BASE_DIR = Path(__file__).parent.parent
UPLOAD_DIR = BASE_DIR / "uploads"
UPLOAD_DIR.mkdir(exist_ok=True)

# 挂载静态文件目录
app.mount("/static", NoCacheStaticFiles(directory=str(UPLOAD_DIR)), name="static")

# 包含路由（API路由会优先匹配）
app.include_router(auth.router)
app.include_router(properties.router)
app.include_router(tenants.router)
app.include_router(leases.router)
app.include_router(transactions.router)
app.include_router(maintenance.router)
app.include_router(notifications.router)
app.include_router(upload.router)
from .routers import parking
app.include_router(parking.router)

# 根路由
@app.get("/")
def read_root():
    return {"message": "欢迎使用租房管理系统API"}

# 健康检查
@app.get("/health")
def health_check():
    return {"status": "healthy", "database": "connected" if check_connection() else "disconnected"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)