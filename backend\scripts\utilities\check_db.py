import os
import sys

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.database import engine
from sqlalchemy import text

# Check if the parking_spaces table exists and its structure
try:
    # Get table names
    result = engine.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
    tables = [row[0] for row in result]
    print("Tables in database:")
    for table in tables:
        print(f"  {table}")
    
    # Check parking_spaces table structure
    if 'parking_spaces' in tables:
        print("\nParking Spaces Table Structure:")
        result = engine.execute(text("SELECT name, type, nullable FROM pragma_table_info('parking_spaces')"))
        for row in result:
            print(f"  {row[0]}: {row[1]} (nullable: {row[2]})")
    else:
        print("\nTable 'parking_spaces' not found")
        
except Exception as e:
    print(f"Error: {e}")