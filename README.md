# 房屋租赁管理系统部署指南

## 项目概述

这是一个基于 FastAPI + React + TypeScript 的房屋租赁管理系统，支持房屋信息管理、图片上传、租客管理等功能。

## 技术栈

### 后端
- **FastAPI**: Python Web 框架
- **SQLAlchemy 2.0**: ORM 框架
- **SQL Server 2008 R2**: 数据库
- **JWT**: 用户认证
- **Uvicorn**: ASGI 服务器

### 前端
- **React 18**: 前端框架
- **TypeScript**: 类型安全
- **Vite**: 构建工具
- **Tailwind CSS**: 样式框架
- **Shadcn/ui**: UI 组件库

## 环境要求

- **Python**: 3.8+
- **Node.js**: 16+
- **SQL Server**: 2008 R2+
- **操作系统**: Windows 10/11 (推荐)

## 更新日志

### 2025-09-02
- 重构了车位管理模块的create_parking_space函数，使用SQLAlchemy ORM替代原生SQL
- 重构了租赁合同管理模块的create_lease函数，使用SQLAlchemy ORM替代原生SQL

## 快速启动 (Windows)

项目提供了便捷的批处理脚本来快速启动服务：

### 一键启动所有服务
```bash
# 双击运行或在命令行执行
start.bat
```
这将同时启动后端和前端服务，后端运行在 http://localhost:8000，前端运行在 http://localhost:5173

### 单独启动服务
```bash
# 仅启动后端服务
start-backend.bat

# 仅启动前端服务
start-frontend.bat
```

**注意**:
- 首次运行前端服务时，脚本会自动安装npm依赖包
- 首次运行后端服务前，请确保已安装Python依赖：`pip install -r requirements.txt`

## 后端部署

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd 房屋租赁管理系统

# 进入后端目录
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据库配置

编辑 `backend/config/database.ini` 文件：

```ini
[database]
server = localhost
database = RentalManagement
username = your_username
password = your_password
driver = ODBC Driver 17 for SQL Server
port = 1433
trust_connection = no

[jwt]
secret_key = your-secret-key-here
algorithm = HS256
access_token_expire_minutes = 2880
```

### 3. 数据库初始化

```bash
# 创建数据库表
python app/init_db.py

# 或者使用独立脚本
python create_tables.py
```

### 4. 启动后端服务

```bash
# 开发模式启动
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 生产模式启动
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

后端服务将在 `http://localhost:8000` 启动

### 5. API 文档

启动后访问：
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 前端部署

### 1. 环境准备

```bash
# 进入前端目录
cd rental-management-app

# 安装依赖
npm install
# 或使用 yarn
yarn install
```

### 2. 环境配置

创建 `.env` 文件来配置API地址：

```env
# 本地开发
VITE_API_BASE_URL=http://localhost:8000

# 局域网访问（将IP改为你的电脑IP地址）
# VITE_API_BASE_URL=http://*************:8000
```

**局域网配置说明：**
- 如需手机等设备访问，请将localhost改为你的电脑IP地址
- 获取IP：Windows命令行输入 `ipconfig` 查看IPv4地址
- 详细配置请参考 `局域网配置说明.md`

### 3. 开发模式启动

```bash
# 启动开发服务器
npm run dev
# 或
yarn dev
```

前端开发服务器将在 `http://localhost:5173` 启动

### 4. 生产构建

```bash
# 构建生产版本
npm run build
# 或
yarn build

# 预览生产构建
npm run preview
# 或
yarn preview
```

构建文件将生成在 `dist/` 目录中

## 生产部署

### 后端生产部署

#### 方式一：直接部署

```bash
# 安装生产依赖
pip install gunicorn

# 启动生产服务器
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

#### 方式二：使用 PM2

```bash
# 安装 PM2
npm install -g pm2

# 创建 ecosystem.config.js
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'rental-backend',
    script: 'uvicorn',
    args: 'app.main:app --host 0.0.0.0 --port 8000',
    cwd: './backend',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
}
EOF

# 启动服务
pm2 start ecosystem.config.js
```

### 前端生产部署

#### 方式一：静态文件服务器

```bash
# 构建项目
npm run build

# 使用 serve 部署
npm install -g serve
serve -s dist -l 3000
```

#### 方式二：Nginx 部署

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /path/to/rental-management-app/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 系统配置

### 默认用户账号

- **用户名**: `lnsylyw`
- **密码**: `lnsy_21our`

### 文件上传配置

- **上传目录**: `backend/uploads/properties/{property_id}/{image_type}/`
- **支持格式**: JPG, PNG, GIF, WebP
- **文件大小限制**: 5MB
- **图片类型**: interior(室内), certificate(房证)
- **目录结构**: 按房屋ID和图片类型分类存储

### 数据库表结构

主要数据表：
- `properties`: 房屋信息
- `property_images`: 房屋图片
- `property_features`: 房屋特色
- `property_facilities`: 配套设施
- `users`: 用户信息

## 常见问题

### 1. 数据库连接失败

检查 `database.ini` 配置是否正确，确保 SQL Server 服务正在运行。

### 2. 图片上传失败

确保 `backend/uploads/properties/` 目录存在且有写入权限。

### 3. 前端 API 调用失败

检查后端服务是否正常运行，确认 API 地址配置正确。

### 4. JWT Token 过期

默认 Token 有效期为 48 小时，可在 `database.ini` 中调整 `access_token_expire_minutes`。

## 开发指南

### 后端开发

```bash
# 安装开发依赖
pip install pytest pytest-asyncio httpx

# 运行测试
pytest

# 代码格式化
black app/
isort app/
```

### 前端开发

```bash
# 类型检查
npm run type-check

# 代码格式化
npm run format

# ESLint 检查
npm run lint
```

### v1.0.0 (2025-01-13)
- ✅ 房屋信息管理（增删改查）
- ✅ 图片上传和分类存储
- ✅ 房证照片独立管理
- ✅ 用户认证和授权
- ✅ 响应式界面设计
- ✅ 小图片滑动翻页功能
- ✅ 房屋类型显示优化

## 技术支持

如有问题，请联系开发团队或查看项目文档。

---

**开发团队**: 云福阁 (Yun Fu Ge)  
**最后更新**: 2025年1月13日