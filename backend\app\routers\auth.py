from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from datetime import datetime, timedelta, timezone
from jose import JWTError, jwt
from passlib.context import CryptContext
from typing import Annotated
import logging

from ..database import get_db
from ..models import User
from ..schemas import Token, TokenData, UserCreate, User as UserSchema
from ..utils.config_reader import get_jwt_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('Auth')

# 从配置文件读取JWT配置
jwt_config = get_jwt_config()
SECRET_KEY: str = str(jwt_config['secret_key'])
ALGORITHM: str = str(jwt_config['algorithm'])
ACCESS_TOKEN_EXPIRE_MINUTES: int = int(jwt_config['access_token_expire_minutes'])

# 密码哈希
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/token")

router = APIRouter(
    prefix="/auth",
    tags=["认证"],
    responses={401: {"description": "未授权"}},
)

# 验证密码
def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

# 获取密码哈希
def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

# 获取用户
def get_user(db: Session, username: str) -> User | None:
    return db.query(User).filter(User.username == username).first()  # type: ignore[attr-defined, unknown-member-type, call-overload]

# 认证用户
def authenticate_user(db: Session, username: str, password: str) -> User | bool:
    logger.info(f"开始认证用户: {username}")
    user = get_user(db, username)
    if not user:
        logger.warning(f"用户不存在: {username}")
        return False
    
    logger.info(f"验证用户密码: {username}")
    if not verify_password(password, user.hashed_password):
        logger.warning(f"密码验证失败: {username}")
        return False
    
    logger.info(f"用户认证成功: {username}")
    return user

# 创建访问令牌
def create_access_token(data: dict[str, str], expires_delta: timedelta | None = None) -> str:
    to_encode: dict[str, str | datetime] = dict(data)
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# 获取当前用户
async def get_current_user(
    token: Annotated[str, Depends(oauth2_scheme)], 
    db: Annotated[Session, Depends(get_db)]
) -> User:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str | None = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    
    user = get_user(db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

# 获取当前活跃用户
async def get_current_active_user(
    current_user: Annotated[UserSchema, Depends(get_current_user)]
) -> UserSchema:
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="用户未激活")
    return current_user

# 登录获取令牌
@router.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends(OAuth2PasswordRequestForm)],
    db: Annotated[Session, Depends(get_db)]
):
    logger.info(f"收到登录请求，用户名: {form_data.username}")
    
    try:
        user = authenticate_user(db, form_data.username, form_data.password)
        if not user or isinstance(user, bool):
            logger.warning(f"登录失败 - 认证失败: {form_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        logger.info(f"开始生成访问令牌，用户: {user.username}")
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username}, expires_delta=access_token_expires
        )
        
        logger.info(f"登录成功，用户: {user.username}, 令牌过期时间: {ACCESS_TOKEN_EXPIRE_MINUTES}分钟")
        return {"access_token": access_token, "token_type": "bearer"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录过程中发生异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误"
        )

# 注册新用户
@router.post("/register", response_model=UserSchema)
async def register_user(
    user: UserCreate, 
    db: Annotated[Session, Depends(get_db)]
):
    db_user = get_user(db, username=user.username)
    if db_user:
        raise HTTPException(status_code=400, detail="用户名已被注册")
    
    hashed_password = get_password_hash(user.password)
    db_user = User(
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        phone=user.phone,
        hashed_password=hashed_password,
        is_active=True
    )
    db.add(db_user)  # type: ignore
    db.commit()  # type: ignore
    db.refresh(db_user)  # type: ignore
    return db_user

# 获取当前用户信息
@router.get("/me", response_model=UserSchema)
async def read_users_me(
    current_user: Annotated[UserSchema, Depends(get_current_active_user)]
):
    return current_user
