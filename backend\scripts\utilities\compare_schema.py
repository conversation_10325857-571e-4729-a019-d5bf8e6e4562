import sys
sys.path.append('c:/Users/<USER>/Documents/retal/backend')
from app.database import engine
from sqlalchemy import text

# 检查数据库中的实际表结构
def check_actual_table_structure():
    with engine.connect() as conn:
        result = conn.execute(text("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'parking_spaces'
            ORDER BY ORDINAL_POSITION
        """))
        columns = result.fetchall()
        print('数据库中实际的parking_spaces表结构:')
        for col in columns:
            print(f'  {col[0]}: {col[1]} (nullable: {col[2]})')

# 检查模型定义
def check_model_definition():
    sys.path.append('c:/Users/<USER>/Documents/retal/backend')
    from app.models import ParkingSpace
    
    # 手动列出模型字段
    print('\n模型定义的字段:')
    # 通过反射获取模型的列定义
    for column in ParkingSpace.__table__.columns:
        print(f'  {column.name}: {column.type}')

if __name__ == '__main__':
    check_actual_table_structure()
    check_model_definition()