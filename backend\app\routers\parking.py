# Copyright (c) 2025 云福阁. All rights reserved.
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Annotated
import logging
from ..database import get_db
from ..models import ParkingSpace, User, Lease
from ..schemas import (
    ParkingSpace as ParkingSpaceSchema,
    ParkingSpaceCreate,
    ParkingSpaceUpdate
)
from .auth import get_current_active_user

# 配置日志
logger = logging.getLogger('Parking')

router = APIRouter(
    prefix="/parking",
    tags=["车位管理"],
    responses={404: {"description": "未找到"}},
)

# 获取车位列表
@router.get("/", response_model=list[ParkingSpaceSchema])
def read_parking_spaces(
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    skip: int = 0, 
    limit: int = 100,
    status: str | None = None,
    parking_type: str | None = None
):
    try:
        logger.info(f"用户 {current_user.username} 请求获取车位列表")
        
        query = db.query(ParkingSpace).filter(ParkingSpace.owner_id == current_user.id)
        
        if status:
            query = query.filter(ParkingSpace.status == status)
        
        if parking_type:
            query = query.filter(ParkingSpace.parking_type == parking_type)
        
        parking_spaces = query.order_by(ParkingSpace.created_at.desc()).offset(skip).limit(limit).all()
        logger.info(f"返回 {len(parking_spaces)} 个车位")
        return parking_spaces
        
    except Exception as e:
        logger.error(f"获取车位列表时出错: {str(e)}")
        # 如果是数据库表不存在的错误，返回空列表
        if "Invalid object name" in str(e) or "doesn't exist" in str(e):
            logger.warning("ParkingSpace表可能不存在，返回空列表")
            return []
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取车位列表失败: {str(e)}"
        )

# 创建车位
@router.post("/", response_model=ParkingSpaceSchema, status_code=status.HTTP_201_CREATED)
def create_parking_space(
    parking_data: ParkingSpaceCreate, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> ParkingSpace:
    """创建车位"""
    logger.info(f"用户 {current_user.username} 请求创建车位")
    logger.info(f"车位数据: {parking_data.model_dump()}")
    
    try:
        # 创建车位数据
        parking_dict = parking_data.model_dump()
        parking_dict['owner_id'] = current_user.id
        
        # 使用SQLAlchemy ORM创建车位
        db_parking = ParkingSpace(**parking_dict)
        db.add(db_parking)
        db.commit()
        db.refresh(db_parking)
        
        logger.info(f"车位创建成功，ID: {db_parking.id}")
        return db_parking
        
    except Exception as e:
        logger.error(f"创建车位失败: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建车位失败: {str(e)}"
        )

# 获取单个车位
@router.get("/{parking_id}", response_model=ParkingSpaceSchema)
def read_parking_space(
    parking_id: int, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> ParkingSpace:
    try:
        logger.info(f"用户 {current_user.username} 请求获取车位详情，parking_id={parking_id}")
        
        db_parking = db.query(ParkingSpace).filter(
            ParkingSpace.id == parking_id,
            ParkingSpace.owner_id == current_user.id
        ).first()
        
        if db_parking is None:
            logger.warning(f"车位 {parking_id} 不存在或无权访问")
            raise HTTPException(status_code=404, detail="车位未找到")
        
        logger.info(f"成功获取车位 {parking_id} 的详情")
        return db_parking
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取车位详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取车位详情失败: {str(e)}"
        )

# 更新车位
@router.put("/{parking_id}", response_model=ParkingSpaceSchema)
def update_parking_space(
    parking_id: int, 
    parking_data: ParkingSpaceUpdate, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> ParkingSpace:
    try:
        logger.info(f"用户 {current_user.username} 请求更新车位，parking_id={parking_id}")
        logger.info(f"更新数据: {parking_data.model_dump(exclude_unset=True)}")
        
        # 权限校验：必须是自己的车位
        db_parking = db.query(ParkingSpace).filter(
            ParkingSpace.id == parking_id, 
            ParkingSpace.owner_id == current_user.id
        ).first()
        if db_parking is None:
            logger.warning(f"车位 {parking_id} 不存在或无权访问")
            raise HTTPException(status_code=404, detail="车位未找到")
        
        # 更新车位信息
        update_data = parking_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_parking, field, value)
        
        db.commit()
        db.refresh(db_parking)
        
        logger.info(f"车位 {parking_id} 更新成功")
        return db_parking
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新车位失败: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新车位失败: {str(e)}"
        )

# 删除车位
@router.delete("/{parking_id}")
def delete_parking_space(
    parking_id: int, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    try:
        logger.info(f"用户 {current_user.username} 请求删除车位，parking_id={parking_id}")
        
        # 权限校验：必须是自己的车位
        db_parking = db.query(ParkingSpace).filter(
            ParkingSpace.id == parking_id, 
            ParkingSpace.owner_id == current_user.id
        ).first()
        if db_parking is None:
            logger.warning(f"车位 {parking_id} 不存在或无权访问")
            raise HTTPException(status_code=404, detail="车位未找到")
        
        # 检查是否有关联的租赁合同
        lease_count = db.query(Lease).filter(
            Lease.parking_space_id == parking_id,
            Lease.status == '生效中'
        ).count()
        if lease_count > 0:
            logger.warning(f"车位 {parking_id} 有生效中的租赁合同，无法删除")
            raise HTTPException(status_code=400, detail="该车位有生效中的租赁合同，无法删除")
        
        db.delete(db_parking)
        db.commit()
        
        logger.info(f"车位 {parking_id} 删除成功")
        return {"message": "车位删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除车位失败: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除车位失败: {str(e)}"
        )

# 获取车位统计信息
@router.get("/statistics/overview")
def get_parking_statistics(
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    try:
        logger.info(f"用户 {current_user.username} 请求获取车位统计信息")
        
        # 总车位数
        total = db.query(ParkingSpace).filter(ParkingSpace.owner_id == current_user.id).count()
        
        # 已出租车位数
        rented = db.query(ParkingSpace).filter(
            ParkingSpace.owner_id == current_user.id,
            ParkingSpace.status == "已出租"
        ).count()
        
        # 可用车位数
        available = db.query(ParkingSpace).filter(
            ParkingSpace.owner_id == current_user.id,
            ParkingSpace.status == "可用"
        ).count()
        
        # 维修中车位数
        maintenance = db.query(ParkingSpace).filter(
            ParkingSpace.owner_id == current_user.id,
            ParkingSpace.status == "维修中"
        ).count()
        
        statistics = {
            "total": total,
            "rented": rented,
            "available": available,
            "maintenance": maintenance
        }
        
        logger.info(f"车位统计信息: {statistics}")
        return statistics
        
    except Exception as e:
        logger.error(f"获取车位统计信息失败: {str(e)}")
        # 如果是数据库表不存在的错误，返回默认值
        if "Invalid object name" in str(e) or "doesn't exist" in str(e):
            logger.warning("ParkingSpace表可能不存在，返回默认统计信息")
            return {"total": 0, "rented": 0, "available": 0, "maintenance": 0}
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取车位统计信息失败: {str(e)}"
        )


