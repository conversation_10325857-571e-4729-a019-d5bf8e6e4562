#!/usr/bin/env python3
# 测试数据库连接和数据查询

import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from sqlalchemy.orm import Session
from app.database import SessionLocal
from app.models import Tenant, Lease, Property, ParkingSpace, User

def test_database():
    db = SessionLocal()
    try:
        print("=== 数据库连接测试 ===")
        
        # 检查用户
        users = db.query(User).all()
        print(f"用户总数: {len(users)}")
        for user in users:
            print(f"  用户: {user.username} (ID: {user.id})")
        
        # 检查租客
        tenants = db.query(Tenant).all()
        print(f"租客总数: {len(tenants)}")
        for tenant in tenants[:5]:  # 只显示前5个
            print(f"  租客: {tenant.name} (ID: {tenant.id})")
        
        # 检查租赁合同
        leases = db.query(Lease).all()
        print(f"租赁合同总数: {len(leases)}")
        for lease in leases[:5]:  # 只显示前5个
            print(f"  合同: ID={lease.id}, 租客ID={lease.tenant_id}, 类型={lease.lease_type}, 状态={lease.status}")
        
        # 检查房屋
        properties = db.query(Property).all()
        print(f"房屋总数: {len(properties)}")
        for prop in properties[:5]:
            print(f"  房屋: {prop.name} (ID: {prop.id}, 业主ID: {prop.owner_id})")
        
        # 检查车位
        parkings = db.query(ParkingSpace).all()
        print(f"车位总数: {len(parkings)}")
        for parking in parkings[:5]:
            print(f"  车位: {parking.name} (ID: {parking.id}, 业主ID: {parking.owner_id})")
        
        # 检查关联关系
        if users:
            user = users[0]
            user_leases = db.query(Lease).join(Property).filter(Property.owner_id == user.id).all()
            user_leases += db.query(Lease).join(ParkingSpace).filter(ParkingSpace.owner_id == user.id).all()
            print(f"\n用户 {user.username} 的租赁合同: {len(user_leases)}")
            
            # 获取该用户的租客
            user_tenants = set()
            for lease in user_leases:
                user_tenants.add(lease.tenant_id)
            print(f"用户 {user.username} 的租客: {len(user_tenants)}")
            
            for tenant_id in list(user_tenants)[:5]:
                tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
                if tenant:
                    print(f"  租客: {tenant.name}")
        
    except Exception as e:
        print(f"数据库查询错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_database()