"""
数据库连接模块
提供与SQL Server数据库的连接和会话管理
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session, DeclarativeBase
import pyodbc as _pyodbc  # 保留以确保 mssql+pyodbc 驱动可用
_ = _pyodbc  # 引用以避免基于 Pyright 的未使用导入告警
import sys
import logging
from collections.abc import Generator

# 导入配置读取模块
from .utils.config_reader import get_db_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('Database')

# 从配置文件读取SQL Server连接参数
db_config = get_db_config()
if not db_config:
    logger.error("无法读取数据库配置，应用将无法正常工作")
    sys.exit(1)  # 如果无法读取配置，直接退出程序

SERVER = db_config['server']
DATABASE = db_config['database']
USERNAME = db_config['username']
PASSWORD = db_config['password']
DRIVER = db_config['driver']
logger.info(f"成功读取数据库配置: {SERVER}/{DATABASE}")

# 构建SQL Server连接URL
SQLALCHEMY_DATABASE_URL = f"mssql+pyodbc://{USERNAME}:{PASSWORD}@{SERVER}/{DATABASE}?driver={DRIVER}"

# 创建SQLAlchemy引擎
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    fast_executemany=True,
    use_setinputsizes=False,
    pool_pre_ping=True,
    echo=False
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基类（SQLAlchemy 2.0）
class Base(DeclarativeBase):
    pass

# 获取数据库会话的依赖项
def get_db() -> Generator[Session, None, None]:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()