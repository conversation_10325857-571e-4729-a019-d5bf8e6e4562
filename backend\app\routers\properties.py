# Copyright (c) 2025 云福阁. All rights reserved.
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Annotated
import logging
from ..database import get_db
from ..models import Property, User, PropertyImage, PropertyFeature, PropertyFacility
from ..schemas import (
    Property as PropertySchema, PropertyCreate, PropertyUpdate
)
from ..services.property_service import PropertyService
from .auth import get_current_active_user

# 配置日志
logger = logging.getLogger('Properties')

router = APIRouter(
    prefix="/properties",
    tags=["房屋管理"],
    responses={404: {"description": "未找到"}},
)

# 获取所有房屋
@router.get("/", response_model=list[PropertySchema])
def read_properties(
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    skip: int = 0,
    limit: int = 100,
    status: str | None = None
) -> list[Property]:
    try:
        logger.info(f"用户 {current_user.username} 请求获取房屋列表，skip={skip}, limit={limit}, status={status}")

        # 构建状态过滤条件
        status_filter = ""
        query_params = {
            'owner_id': current_user.id,
            'skip': skip,
            'end_row': skip + limit
        }

        if status:
            status_filter = "AND ISNULL(status, '可用') = :status"
            query_params['status'] = status

        # 使用兼容的ROW_NUMBER()分页语法处理NULL值
        result = db.execute(text(f"""
            WITH PropertyData AS (
                SELECT
                    id, name, address, city, province, postal_code, property_type,
                    area,
                    ISNULL(rooms, 1) as rooms,
                    ISNULL(living_rooms, 1) as living_rooms,
                    ISNULL(bathrooms, 1) as bathrooms,
                    floor, total_floors, orientation, decoration_status,
                    has_elevator, has_parking, description,
                    monthly_rent, deposit,
                    ISNULL(payment_method, '押一付三') as payment_method,
                    ISNULL(min_lease_months, 12) as min_lease_months,
                    ISNULL(status, '可用') as status,
                    ISNULL(created_at, GETDATE()) as created_at,
                    ISNULL(updated_at, GETDATE()) as updated_at,
                    owner_id,
                    ROW_NUMBER() OVER (ORDER BY id) as row_num
                FROM properties
                WHERE owner_id = :owner_id {status_filter}
            )
            SELECT
                id, name, address, city, province, postal_code, property_type,
                area, rooms, living_rooms, bathrooms, floor, total_floors,
                orientation, decoration_status, has_elevator, has_parking,
                description, monthly_rent, deposit, payment_method,
                min_lease_months, status, created_at, updated_at, owner_id
            FROM PropertyData
            WHERE row_num > :skip AND row_num <= :end_row
            ORDER BY id
        """), query_params)
        
        # 手动构建Property对象列表并加载关联数据
        properties = []
        for row in result.fetchall():
            property_dict = {
                'id': row[0],
                'name': row[1],
                'address': row[2],
                'city': row[3],
                'province': row[4],
                'postal_code': row[5],
                'property_type': row[6],
                'area': float(row[7]) if row[7] else 0.0,
                'rooms': row[8],
                'living_rooms': row[9],
                'bathrooms': row[10],
                'floor': row[11],
                'total_floors': row[12],
                'orientation': row[13],
                'decoration_status': row[14],
                'has_elevator': bool(row[15]) if row[15] is not None else False,
                'has_parking': bool(row[16]) if row[16] is not None else False,
                'description': row[17],
                'monthly_rent': float(row[18]) if row[18] else 0.0,
                'deposit': float(row[19]) if row[19] else 0.0,
                'payment_method': row[20],
                'min_lease_months': row[21],
                'status': row[22],
                'created_at': row[23],
                'updated_at': row[24],
                'owner_id': row[25]
            }
            
            # 创建Property对象
            property_obj = Property(**property_dict)
            
            # 手动加载关联的图片、特色和设施数据
            property_obj.images = db.query(PropertyImage).filter(PropertyImage.property_id == property_obj.id).order_by(PropertyImage.sort_order, PropertyImage.id).all()
            property_obj.features = db.query(PropertyFeature).filter(PropertyFeature.property_id == property_obj.id).all()
            property_obj.facilities = db.query(PropertyFacility).filter(PropertyFacility.property_id == property_obj.id).all()
            
            properties.append(property_obj)
        
        logger.info(f"找到 {len(properties)} 个房屋记录")
        return properties
        
    except Exception as e:
        logger.error(f"获取房屋列表时出错: {str(e)}")
        # 如果是数据库表不存在的错误，返回空列表
        if "Invalid object name" in str(e) or "doesn't exist" in str(e):
            logger.warning("Property表可能不存在，返回空列表")
            return []
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取房屋列表失败: {str(e)}"
        )

# 创建房屋
@router.post("/", response_model=PropertySchema, status_code=status.HTTP_201_CREATED)
def create_property(
    property_data: PropertyCreate, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> Property:
    """创建房屋及其关联数据（图片、特色、设施）"""
    logger.info(f"用户 {current_user.username} 请求创建房屋")
    logger.info(f"=== 后端收到的数据调试 ===")
    logger.info(f"原始数据: {property_data.model_dump()}")
    logger.info(f"=== 调试信息结束 ===")
    
    # 开始显式事务
    try:
        # 重置连接状态，避免函数序列错误
        db.execute(text("SELECT 1"))
        db.commit()
        
        # 开始新事务
        db.begin()
        
        # 1. 创建房屋主记录
        property_dict = property_data.model_dump(exclude={'images', 'features', 'facilities'})
        property_dict['owner_id'] = current_user.id
        
        # 使用更可靠的方法：基于唯一条件查询最新记录
        import time
        current_timestamp = int(time.time() * 1000)  # 毫秒时间戳作为唯一标识
        
        # 在description中添加时间戳确保唯一性
        original_description = property_dict.get('description', '')
        property_dict['description'] = f"{original_description}##TEMP_ID_{current_timestamp}##"
        
        _ = db.execute(text("""
            INSERT INTO properties (
                name, address, city, province, postal_code, property_type, 
                area, rooms, living_rooms, bathrooms, floor, total_floors, 
                orientation, decoration_status, has_elevator, has_parking, 
                description, monthly_rent, deposit, payment_method, 
                min_lease_months, status, owner_id, created_at, updated_at
            ) VALUES (
                :name, :address, :city, :province, :postal_code, :property_type,
                :area, :rooms, :living_rooms, :bathrooms, :floor, :total_floors,
                :orientation, :decoration_status, :has_elevator, :has_parking,
                :description, :monthly_rent, :deposit, :payment_method,
                :min_lease_months, :status, :owner_id, GETDATE(), GETDATE()
            )
        """), property_dict)
        
        # 通过临时标识查询新插入的记录
        result = db.execute(text("""
            SELECT id FROM properties 
            WHERE owner_id = :owner_id AND description LIKE :temp_desc
            ORDER BY id DESC
        """), {
            'owner_id': current_user.id,
            'temp_desc': f"%##TEMP_ID_{current_timestamp}##%"
        })
        
        property_id_row = result.fetchone()
        if not property_id_row or not property_id_row[0]:
            raise Exception("无法获取新创建的房屋ID")
        
        property_id = int(property_id_row[0])
        
        # 恢复原始description
        _ = db.execute(text("""
            UPDATE properties 
            SET description = :original_desc 
            WHERE id = :property_id
        """), {
            'original_desc': original_description,
            'property_id': property_id
        })
        logger.info(f"房屋主记录创建成功，ID: {property_id}")
        
        # 2. 创建房屋图片
        if hasattr(property_data, 'images') and property_data.images:
            for image_data in property_data.images:
                _ = db.execute(text("""
                    INSERT INTO property_images (
                        property_id, image_url, image_type, description, 
                        sort_order, is_cover, created_at
                    ) VALUES (
                        :property_id, :image_url, :image_type, :description,
                        :sort_order, :is_cover, GETDATE()
                    )
                """), {
                    'property_id': property_id,
                    'image_url': image_data.image_url,
                    'image_type': image_data.image_type,
                    'description': image_data.description,
                    'sort_order': image_data.sort_order,
                    'is_cover': image_data.is_cover
                })
            logger.info(f"创建了 {len(property_data.images)} 张房屋图片")
        
        # 3. 创建房屋特色
        if hasattr(property_data, 'features') and property_data.features:
            for feature_data in property_data.features:
                _ = db.execute(text("""
                    INSERT INTO property_features (
                        property_id, feature_name, feature_value, created_at
                    ) VALUES (
                        :property_id, :feature_name, :feature_value, GETDATE()
                    )
                """), {
                    'property_id': property_id,
                    'feature_name': feature_data.feature_name,
                    'feature_value': feature_data.feature_value
                })
            logger.info(f"创建了 {len(property_data.features)} 个房屋特色")
        
        # 4. 创建房屋设施
        if hasattr(property_data, 'facilities') and property_data.facilities:
            for facility_data in property_data.facilities:
                _ = db.execute(text("""
                    INSERT INTO property_facilities (
                        property_id, facility_name, facility_category, 
                        is_available, description, created_at
                    ) VALUES (
                        :property_id, :facility_name, :facility_category,
                        :is_available, :description, GETDATE()
                    )
                """), {
                    'property_id': property_id,
                    'facility_name': facility_data.facility_name,
                    'facility_category': facility_data.facility_category,
                    'is_available': facility_data.is_available,
                    'description': facility_data.description
                })
            logger.info(f"创建了 {len(property_data.facilities)} 个房屋设施")
        
        # 提交事务
        db.commit()
        logger.info(f"房屋及关联数据创建成功，事务已提交")
        
        # 查询完整的房屋信息返回
        created_property = db.query(Property).filter(Property.id == property_id).first()
        if not created_property:
            raise Exception("无法查询到刚创建的房屋")
        
        return created_property
        
    except Exception as e:
        logger.error(f"创建房屋时出错: {str(e)}")
        try:
            db.rollback()
            logger.info("事务已回滚")
        except Exception as rollback_error:
            logger.error(f"回滚事务时出错: {str(rollback_error)}")
        
        if isinstance(e, HTTPException):
            raise
        raise HTTPException(status_code=500, detail=f"创建房屋失败: {str(e)}")

# 获取单个房屋
@router.get("/{property_id}", response_model=PropertySchema)
def read_property(
    property_id: int, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> Property:
    try:
        logger.info(f"用户 {current_user.username} 请求获取房屋详情，property_id={property_id}")
        
        # 使用PropertyService获取完整的房屋信息
        property_service = PropertyService(db)
        db_property = property_service.get_property_with_details(property_id)
        
        if db_property is None:
            logger.warning(f"房屋 {property_id} 不存在")
            raise HTTPException(status_code=404, detail="房屋未找到")
        
        # 检查权限
        if db_property.owner_id != current_user.id:
            logger.warning(f"用户 {current_user.username} 无权访问房屋 {property_id}")
            raise HTTPException(status_code=403, detail="无权访问此房屋")
        
        logger.info(f"成功获取房屋详情: {db_property.name}")
        return db_property
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取房屋详情时出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取房屋详情失败: {str(e)}"
        )

# 更新房屋
@router.put("/{property_id}", response_model=PropertySchema)
def update_property(
    property_id: int, 
    property_data: PropertyUpdate, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> Property:
    try:
        logger.info(f"用户 {current_user.username} 请求更新房屋，property_id={property_id}")
        logger.info(f"更新数据: {property_data.model_dump(exclude_unset=True)}")
        
        # 权限校验：必须是自己的房屋
        db_property = db.query(Property).filter(
            Property.id == property_id, 
            Property.owner_id == current_user.id
        ).first()
        if db_property is None:
            logger.warning(f"房屋 {property_id} 不存在或无权访问")
            raise HTTPException(status_code=404, detail="房屋未找到")
        
        # 使用服务层以同时更新关联数据：图片、特色、设施
        service = PropertyService(db)
        updated = service.update_property_with_details(property_id, property_data)
        if updated is None:
            raise HTTPException(status_code=500, detail="更新房屋失败")
        
        logger.info(f"房屋更新成功: {updated.name}")
        return updated
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新房屋时出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新房屋失败: {str(e)}"
        )

# 更新房屋特色
@router.put("/{property_id}/features")
def update_property_features(
    property_id: int,
    payload: dict[str, list[dict[str, str | None]]],  # 接收包装的数据格式 {"features": [...]}
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> dict[str, str | int]:
    try:
        logger.info(f"用户 {current_user.username} 请求更新房屋特色，property_id={property_id}")
        
        # 权限校验
        db_property = db.query(Property).filter(
            Property.id == property_id, 
            Property.owner_id == current_user.id
        ).first()
        if db_property is None:
            logger.warning(f"房屋 {property_id} 不存在或无权访问")
            raise HTTPException(status_code=404, detail="房屋未找到")
        
        # 删除现有特色
        _ = db.query(PropertyFeature).filter(PropertyFeature.property_id == property_id).delete()
        
        # 从payload中提取features数组
        features = payload.get("features", [])
        
        # 添加新特色
        for feature_data in features:
            db_feature = PropertyFeature(
                property_id=property_id,
                feature_name=feature_data.get("feature_name", ""),
                feature_value=feature_data.get("feature_value")
            )
            db.add(db_feature)
        
        db.commit()
        logger.info(f"房屋特色更新成功，共 {len(features)} 个特色")
        return {"message": "房屋特色更新成功", "count": len(features)}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新房屋特色时出错: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新房屋特色失败: {str(e)}"
        )

# 更新房屋设施
@router.put("/{property_id}/facilities")
def update_property_facilities(
    property_id: int,
    payload: dict[str, list[dict[str, str | bool | None]]],  # 接收包装的数据格式 {"facilities": [...]}
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> dict[str, str | int]:
    try:
        logger.info(f"用户 {current_user.username} 请求更新房屋设施，property_id={property_id}")
        
        # 权限校验
        db_property = db.query(Property).filter(
            Property.id == property_id, 
            Property.owner_id == current_user.id
        ).first()
        if db_property is None:
            logger.warning(f"房屋 {property_id} 不存在或无权访问")
            raise HTTPException(status_code=404, detail="房屋未找到")
        
        # 删除现有设施
        _ = db.query(PropertyFacility).filter(PropertyFacility.property_id == property_id).delete()
        
        # 从payload中提取facilities数组
        facilities = payload.get("facilities", [])
        
        # 添加新设施
        for facility_data in facilities:
            db_facility = PropertyFacility(
                property_id=property_id,
                facility_name=facility_data.get("facility_name", ""),
                facility_category=facility_data.get("facility_category", "其他"),
                is_available=facility_data.get("is_available", True),
                description=facility_data.get("description")
            )
            db.add(db_facility)
        
        db.commit()
        logger.info(f"房屋设施更新成功，共 {len(facilities)} 个设施")
        return {"message": "房屋设施更新成功", "count": len(facilities)}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新房屋设施时出错: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新房屋设施失败: {str(e)}"
        )

# 更新房屋图片
@router.put("/{property_id}/images")
def update_property_images(
    property_id: int,
    payload: dict[str, list[dict[str, str | int | bool | None]]],  # 接收包装的数据格式 {"images": [...]}
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> dict[str, str | int]:
    try:
        logger.info(f"用户 {current_user.username} 请求更新房屋图片，property_id={property_id}")
        
        # 权限校验
        db_property = db.query(Property).filter(
            Property.id == property_id, 
            Property.owner_id == current_user.id
        ).first()
        if db_property is None:
            logger.warning(f"房屋 {property_id} 不存在或无权访问")
            raise HTTPException(status_code=404, detail="房屋未找到")
        
        # 删除现有图片
        _ = db.query(PropertyImage).filter(PropertyImage.property_id == property_id).delete()
        
        # 从payload中提取images数组
        images = payload.get("images", [])
        
        # 添加新图片
        for image_data in images:
            db_image = PropertyImage(
                property_id=property_id,
                image_url=image_data.get("image_url", ""),
                image_type=image_data.get("image_type", "interior"),
                description=image_data.get("description"),
                sort_order=image_data.get("sort_order", 0),
                is_cover=image_data.get("is_cover", False)
            )
            db.add(db_image)
        
        db.commit()
        logger.info(f"房屋图片更新成功，共 {len(images)} 张图片")
        return {"message": "房屋图片更新成功", "count": len(images)}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新房屋图片时出错: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新房屋图片失败: {str(e)}"
        )

# 删除房屋
@router.delete("/{property_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_property(
    property_id: int, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> None:
    db_property = db.query(Property).filter(
        Property.id == property_id, 
        Property.owner_id == current_user.id
    ).first()
    if db_property is None:
        raise HTTPException(status_code=404, detail="房屋未找到")
    
    db.delete(db_property)
    db.commit()