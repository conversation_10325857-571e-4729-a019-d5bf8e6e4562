{"include": ["backend/app"], "exclude": ["**/__pycache__", "**/.venv", "**/venv", "**/node_modules"], "reportCallInDefaultInitializer": "none", "reportMissingImports": "warning", "reportMissingTypeStubs": "none", "reportUnknownMemberType": "none", "reportUnknownVariableType": "none", "reportUnknownArgumentType": "none", "reportAttributeAccessIssue": "none", "reportOptionalMemberAccess": "none", "reportGeneralTypeIssues": "none", "pythonVersion": "3.11", "pythonPlatform": "All", "typeCheckingMode": "basic", "strictParameterNoneValue": false, "strictDictionaryInference": false, "strictListInference": false, "strictSetInference": false}