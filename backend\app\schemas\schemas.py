from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import Optional
from app.models import (
    TransactionType, TransactionCategory, 
    MaintenanceStatus, MaintenancePriority, NotificationType
)

# 用户模式
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: str | None = None
    phone: str | None = None

class UserCreate(UserBase):
    password: str

class User(UserBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes: bool = True

# 令牌模式
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: str

# 房屋图片模式
class PropertyImageBase(BaseModel):
    image_url: str
    image_type: str = "interior"
    description: str | None = None
    sort_order: int = 0
    is_cover: bool = False

class PropertyImageCreate(PropertyImageBase):
    pass

class PropertyImage(PropertyImageBase):
    id: int
    property_id: int
    created_at: datetime

    class Config:
        from_attributes: bool = True

# 房屋特色模式
class PropertyFeatureBase(BaseModel):
    feature_name: str
    feature_value: str | None = None

class PropertyFeatureCreate(PropertyFeatureBase):
    pass

class PropertyFeature(PropertyFeatureBase):
    id: int
    property_id: int
    created_at: datetime

    class Config:
        from_attributes: bool = True

# 房屋设施模式
class PropertyFacilityBase(BaseModel):
    facility_name: str
    facility_category: str
    is_available: bool = True
    description: str | None = None

class PropertyFacilityCreate(PropertyFacilityBase):
    pass

class PropertyFacility(PropertyFacilityBase):
    id: int
    property_id: int
    created_at: datetime

    class Config:
        from_attributes: bool = True

# 房屋模式
class PropertyBase(BaseModel):
    name: str
    address: str
    city: str
    province: str
    postal_code: str | None = None
    property_type: str
    area: float
    rooms: int
    living_rooms: int = 1
    bathrooms: int
    floor: int | None = None
    total_floors: int | None = None
    orientation: str | None = None
    decoration_status: str | None = None
    has_elevator: bool = False
    has_parking: bool = False
    description: str | None = None
    monthly_rent: float
    deposit: float
    payment_method: str = "押一付三"
    min_lease_months: int = 12
    status: str = "可用"

class PropertyCreate(PropertyBase):
    images: list[PropertyImageCreate] | None = None
    features: list[PropertyFeatureCreate] | None = None
    facilities: list[PropertyFacilityCreate] | None = None

class PropertyUpdate(PropertyBase):
    images: list[PropertyImageCreate] | None = None
    features: list[PropertyFeatureCreate] | None = None
    facilities: list[PropertyFacilityCreate] | None = None

class Property(PropertyBase):
    id: int
    created_at: datetime
    updated_at: datetime
    owner_id: int
    images: list[PropertyImage] | None = None
    features: list[PropertyFeature] | None = None
    facilities: list[PropertyFacility] | None = None

    class Config:
        from_attributes: bool = True

# 租客模式（基本信息）
class TenantBase(BaseModel):
    name: str
    phone: str
    email: EmailStr | None = None
    id_card: str
    emergency_contact: str | None = None
    emergency_phone: str | None = None
    notes: str | None = None

class TenantCreate(TenantBase):
    pass

class TenantUpdate(TenantBase):
    pass

class Tenant(TenantBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes: bool = True

# 专门用于租客列表的Schema，包含简化的租赁信息，避免循环引用
class TenantWithLeases(TenantBase):
    id: int
    created_at: datetime
    updated_at: datetime
    leases: list['LeaseForTenant'] | None = None

    class Config:
        from_attributes: bool = True

# 租赁合同模式
class LeaseBase(BaseModel):
    tenant_id: int
    property_id: int | None = None
    parking_space_id: int | None = None
    lease_type: str  # 'property' 或 'parking'
    lease_start: datetime
    lease_end: datetime
    monthly_rent: float
    deposit_paid: float
    payment_method: str = "押一付三"
    car_number: str | None = None  # 车位租赁时必填
    car_model: str | None = None
    status: str = "生效中"
    notes: str | None = None

class LeaseCreate(LeaseBase):
    pass

class LeaseUpdate(BaseModel):
    tenant_id: int | None = None
    property_id: int | None = None
    parking_space_id: int | None = None
    lease_type: str | None = None
    lease_start: datetime | None = None
    lease_end: datetime | None = None
    monthly_rent: float | None = None
    deposit_paid: float | None = None
    payment_method: str | None = None
    car_number: str | None = None
    car_model: str | None = None
    status: str | None = None
    notes: str | None = None

# 专门用于租客列表的租赁Schema，不包含tenant字段避免循环引用
class LeaseForTenant(LeaseBase):
    id: int
    created_at: datetime
    updated_at: datetime

    # 关联对象 - 不包含tenant字段
    property: Optional['Property'] = None
    parking_space: Optional['ParkingSpace'] = None

    class Config:
        from_attributes: bool = True

class Lease(LeaseBase):
    id: int
    created_at: datetime
    updated_at: datetime

    # 关联对象 - 包含tenant字段用于合同管理
    tenant: Optional['Tenant'] = None
    property: Optional['Property'] = None
    parking_space: Optional['ParkingSpace'] = None

    class Config:
        from_attributes: bool = True

# 交易模式
class TransactionBase(BaseModel):
    amount: float
    transaction_type: TransactionType
    category: TransactionCategory
    description: str | None = None
    transaction_date: datetime
    property_id: int
    tenant_id: int | None = None

class TransactionCreate(TransactionBase):
    pass

class TransactionUpdate(TransactionBase):
    pass

class Transaction(TransactionBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes: bool = True

# 维修请求模式
class MaintenanceBase(BaseModel):
    title: str
    description: str
    status: MaintenanceStatus = MaintenanceStatus.PENDING
    priority: MaintenancePriority = MaintenancePriority.MEDIUM
    reported_date: datetime
    scheduled_date: datetime | None = None
    completed_date: datetime | None = None
    cost: float | None = None
    notes: str | None = None
    property_id: int
    tenant_id: int | None = None

class MaintenanceCreate(MaintenanceBase):
    pass

class MaintenanceUpdate(MaintenanceBase):
    pass

class Maintenance(MaintenanceBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes: bool = True

# 通知模式
class NotificationBase(BaseModel):
    title: str
    message: str
    notification_type: NotificationType = NotificationType.INFO

class NotificationCreate(NotificationBase):
    user_id: int

class Notification(NotificationBase):
    id: int
    read: bool
    created_at: datetime
    user_id: int

    class Config:
        from_attributes: bool = True


# 车位模式
class ParkingSpaceBase(BaseModel):
    name: str
    space_number: str = "UNKNOWN"
    location: str
    parking_type: str = "地面车位"
    floor: int | None = None
    zone: str | None = None
    monthly_rent: float
    payment_method: str = "半年付"
    min_lease_months: int = 6
    status: str = "可用"
    description: str | None = None

class ParkingSpaceCreate(ParkingSpaceBase):
    pass

class ParkingSpaceUpdate(ParkingSpaceBase):
    pass

class ParkingSpace(ParkingSpaceBase):
    id: int
    created_at: datetime
    updated_at: datetime
    owner_id: int

    class Config:
        from_attributes: bool = True



