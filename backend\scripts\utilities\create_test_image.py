# Copyright (c) 2025 Aonis. All rights reserved.

from PIL import Image, ImageDraw
import os

def create_test_image():
    """创建一个简单的测试图片文件"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (100, 100), color = 'red')
    d = ImageDraw.Draw(img)
    d.text((10,10), "Test Image", fill=(255,255,0))
    
    # 保存为test.jpg
    img.save('test.jpg')
    print("测试图片 test.jpg 已创建")

if __name__ == "__main__":
    create_test_image()