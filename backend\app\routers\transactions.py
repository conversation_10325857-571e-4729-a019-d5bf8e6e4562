from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Annotated
from datetime import datetime, date
from ..database import get_db
from ..models import Transaction, Property, Tenant, User
from ..schemas import Transaction as TransactionSchema, TransactionCreate, TransactionUpdate
from .auth import get_current_active_user

router = APIRouter(
    prefix="/transactions",
    tags=["财务管理"],
    responses={404: {"description": "未找到"}},
)

# 获取所有交易记录
@router.get("/", response_model=list[TransactionSchema])
def read_transactions(
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    skip: int = 0, 
    limit: int = 100, 
    property_id: int | None = None,
    tenant_id: int | None = None,
    start_date: date | None = None,
    end_date: date | None = None
):
    query = db.query(Transaction).join(Property).filter(Property.owner_id == current_user.id)
    
    if property_id:
        query = query.filter(Transaction.property_id == property_id)
    
    if tenant_id:
        query = query.filter(Transaction.tenant_id == tenant_id)
    
    if start_date:
        query = query.filter(Transaction.transaction_date >= start_date)
    
    if end_date:
        query = query.filter(Transaction.transaction_date <= end_date)
    
    transactions = query.order_by(Transaction.transaction_date.desc()).offset(skip).limit(limit).all()
    return transactions

# 创建交易记录
@router.post("/", response_model=TransactionSchema, status_code=status.HTTP_201_CREATED)
def create_transaction(
    transaction_data: TransactionCreate, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    # 验证房屋是否存在且属于当前用户
    property = db.query(Property).filter(
        Property.id == transaction_data.property_id,
        Property.owner_id == current_user.id
    ).first()
    
    if not property:
        raise HTTPException(status_code=404, detail="指定的房屋不存在或不属于当前用户")
    
    # 租客信息不再需要验证，支出方式中不需要关联租客信息
    
    db_transaction = Transaction(**transaction_data.dict())
    db.add(db_transaction)  # type: ignore[unknown-member-type]
    db.commit()  # type: ignore[unknown-member-type]
    db.refresh(db_transaction)  # type: ignore[unknown-member-type]
    return db_transaction

# 获取单个交易记录
@router.get("/{transaction_id}", response_model=TransactionSchema)
def read_transaction(
    transaction_id: int, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    transaction = db.query(Transaction).join(Property).filter(
        Transaction.id == transaction_id,
        Property.owner_id == current_user.id
    ).first()
    
    if transaction is None:
        raise HTTPException(status_code=404, detail="交易记录未找到")
    return transaction

# 更新交易记录
@router.put("/{transaction_id}", response_model=TransactionSchema)
def update_transaction(
    transaction_id: int, 
    transaction_data: TransactionUpdate, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    # 验证房屋是否存在且属于当前用户
    property = db.query(Property).filter(
        Property.id == transaction_data.property_id,
        Property.owner_id == current_user.id
    ).first()
    
    if not property:
        raise HTTPException(status_code=404, detail="指定的房屋不存在或不属于当前用户")
    
    # 租客信息不再需要验证，支出方式中不需要关联租客信息
    
    transaction = db.query(Transaction).join(Property).filter(
        Transaction.id == transaction_id,
        Property.owner_id == current_user.id
    ).first()
    
    if transaction is None:
        raise HTTPException(status_code=404, detail="交易记录未找到")
    
    for key, value in transaction_data.dict().items():
        setattr(transaction, key, value)
    
    db.commit()
    db.refresh(transaction)
    return transaction

# 删除交易记录
@router.delete("/{transaction_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_transaction(
    transaction_id: int, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    transaction = db.query(Transaction).join(Property).filter(
        Transaction.id == transaction_id,
        Property.owner_id == current_user.id
    ).first()
    
    if transaction is None:
        raise HTTPException(status_code=404, detail="交易记录未找到")
    
    db.delete(transaction)
    db.commit()
    return {"detail": "交易记录已删除"}

# 获取财务统计
@router.get("/statistics/summary")
def get_financial_statistics(
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    year: int | None = None,
    month: int | None = None,
    property_id: int | None = None
):
    from sqlalchemy import func, extract
    from ..models import TransactionType
    
    # 基础查询
    query = db.query(Transaction).join(Property).filter(Property.owner_id == current_user.id)
    
    # 应用过滤条件
    if year:
        query = query.filter(extract('year', Transaction.transaction_date) == year)
    
    if month:
        query = query.filter(extract('month', Transaction.transaction_date) == month)
    
    if property_id:
        query = query.filter(Transaction.property_id == property_id)
    
    # 计算收入总额
    income_query = query.filter(Transaction.transaction_type == TransactionType.INCOME)
    total_income = db.query(func.sum(Transaction.amount)).select_from(income_query.subquery()).scalar() or 0
    
    # 计算支出总额
    expense_query = query.filter(Transaction.transaction_type == TransactionType.EXPENSE)
    total_expense = db.query(func.sum(Transaction.amount)).select_from(expense_query.subquery()).scalar() or 0
    
    # 计算净收入
    net_income = total_income - total_expense
    
    return {
        "total_income": total_income,
        "total_expense": total_expense,
        "net_income": net_income,
        "filters": {
            "year": year,
            "month": month,
            "property_id": property_id
        }
    }