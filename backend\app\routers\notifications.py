from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Annotated
from ..database import get_db
from ..models import Notification, User
from ..schemas import Notification as NotificationSchema
from .auth import get_current_active_user

router = APIRouter(
    prefix="/notifications",
    tags=["通知管理"],
    responses={404: {"description": "未找到"}},
)

# 获取当前用户的所有通知
@router.get("/", response_model=list[NotificationSchema])
def read_notifications(
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    skip: int = 0, 
    limit: int = 100, 
    unread_only: bool = False
):
    query = db.query(Notification).filter(Notification.user_id == current_user.id)
    
    if unread_only:
        query = query.filter(Notification.read == False)
    
    notifications = query.order_by(Notification.created_at.desc()).offset(skip).limit(limit).all()
    return notifications

# 创建通知（系统内部使用，不暴露为API）
def create_notification(
    db: Session,
    user_id: int,
    title: str,
    message: str,
    notification_type: str = "INFO"
):
    notification = Notification(
        user_id=user_id,
        title=title,
        message=message,
        notification_type=notification_type,
        read=False
    )
    db.add(notification)  # type: ignore[unknown-member-type]
    db.commit()  # type: ignore[unknown-member-type]
    db.refresh(notification)  # type: ignore[unknown-member-type]
    return notification

# 获取单个通知
@router.get("/{notification_id}", response_model=NotificationSchema)
def read_notification(
    notification_id: int, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    notification = db.query(Notification).filter(
        Notification.id == notification_id,
        Notification.user_id == current_user.id
    ).first()
    
    if notification is None:
        raise HTTPException(status_code=404, detail="通知未找到")
    return notification

# 标记通知为已读
@router.patch("/{notification_id}/read", response_model=NotificationSchema)
def mark_notification_as_read(
    notification_id: int, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    notification = db.query(Notification).filter(
        Notification.id == notification_id,
        Notification.user_id == current_user.id
    ).first()
    
    if notification is None:
        raise HTTPException(status_code=404, detail="通知未找到")
    
    notification.read = True
    db.commit()
    db.refresh(notification)
    return notification

# 标记所有通知为已读
@router.patch("/read-all", status_code=status.HTTP_204_NO_CONTENT)
def mark_all_notifications_as_read(
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    db.query(Notification).filter(
        Notification.user_id == current_user.id,
        Notification.read == False
    ).update({"read": True})
    
    db.commit()
    return {"detail": "所有通知已标记为已读"}

# 删除通知
@router.delete("/{notification_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_notification(
    notification_id: int, 
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    notification = db.query(Notification).filter(
        Notification.id == notification_id,
        Notification.user_id == current_user.id
    ).first()
    
    if notification is None:
        raise HTTPException(status_code=404, detail="通知未找到")
    
    db.delete(notification)
    db.commit()
    return {"detail": "通知已删除"}

# 获取未读通知数量
@router.get("/count/unread")
def get_unread_notification_count(
    db: Annotated[Session, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    count = db.query(Notification).filter(
        Notification.user_id == current_user.id,
        Notification.read == False
    ).count()
    
    return {"unread_count": count}