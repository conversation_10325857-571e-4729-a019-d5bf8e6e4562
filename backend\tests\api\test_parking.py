import requests
import json

# 测试创建车位的API
url = "http://localhost:8000/parking/"

# 车位数据
parking_data = {
    "name": "御景湾B102",
    "location": "浦江御景湾6号楼地下",
    "parking_type": "地下车位",
    "area": None,
    "floor": -1,
    "zone": "B区",
    "monthly_rent": 630.0,
    "deposit": 630.0,
    "payment_method": "押一付十二",
    "min_lease_months": 12,
    "status": "可用",
    "description": "位置好"
}

# 发送POST请求
response = requests.post(url, json=parking_data)

# 打印响应
print(f"Status Code: {response.status_code}")
print(f"Response: {response.text}")