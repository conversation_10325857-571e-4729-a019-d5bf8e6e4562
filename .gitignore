# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 构建输出
dist/
dist-ssr/
build/
*.local

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.env
.venv

# Python虚拟环境
pip-log.txt
pip-delete-this-directory.txt

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.cache/

# 测试覆盖率
coverage/
*.lcov
.nyc_output

# TypeScript
*.tsbuildinfo

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# Microbundle缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的REPL历史
.node_repl_history

# 输出的npm包
*.tgz

# Yarn完整性文件
.yarn-integrity

# dotenv环境变量文件
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler缓存
.parcel-cache

# Next.js构建输出
.next

# Nuxt.js构建/生成输出
.nuxt
dist

# Gatsby文件
.cache/
# 注意：public文件夹已被注释掉，避免影响前端项目的静态资源文件夹
# public

# Storybook构建输出
.out
.storybook-out

# Rollup.js默认构建输出
.rpt2_cache/

# Serverless目录
.serverless/

# FuseBox缓存
.fusebox/

# DynamoDB本地文件
.dynamodb/

# TernJS端口文件
.tern-port

# FastAPI/Uvicorn
*.pid

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# celery beat调度文件
celerybeat-schedule

# SageMath解析文件
*.sage.py

# 环境配置
.spyderproject
.spyproject

# Rope项目设置
.ropeproject

# mkdocs文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre类型检查器
.pyre/

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 配置文件（可能包含敏感信息）
config.json
secrets.json
.secrets

# 上传文件目录
media/
static/media/

# 证书文件
*.pem
*.key
*.crt
*.cert

# 本地开发配置
.local
local_settings.py