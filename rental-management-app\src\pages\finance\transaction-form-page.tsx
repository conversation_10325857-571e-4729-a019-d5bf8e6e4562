import React from 'react';
import { useParams } from 'react-router-dom';
import { Wallet } from 'lucide-react';
import PageHeader, { PAGE_HEADER_TOP_SPACING } from '@/components/ui/page-header';
import TransactionForm from '@/components/finance/transaction-form';

const TransactionFormPage = () => {
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;

  return (
    <div className="bg-gray-50">
      {/* 顶部导航栏 */}
      <PageHeader
        title={isEditMode ? '编辑交易' : '添加交易'}
        icon={Wallet}
        backgroundColor="bg-amber-600"
        backPath="/finance?tab=transactions"
      />

      {/* 主内容区 */}
      <main className={`container mx-auto ${PAGE_HEADER_TOP_SPACING}`}>
        <TransactionForm editMode={isEditMode} />
      </main>
    </div>
  );
};

export default TransactionFormPage;